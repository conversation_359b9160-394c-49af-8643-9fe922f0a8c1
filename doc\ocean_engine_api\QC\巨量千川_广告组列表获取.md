# 广告组列表获取

## 接口描述
本接口用于获取账户下的广告组列表

## 请求地址
https://api.oceanengine.com/open_api/v1.0/qianchuan/campaign_list/get/

## 请求方法
GET

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Access-Token | string | 必填，授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| advertiser_id | number | 必填，千川广告账户ID |
| filter | object | 必填，过滤器，无过滤条件情况下返回"所有不包含已删除"的广告组列表 |
| └─ ids | number[] | 广告组ID列表，目前只支持一个。 |
| └─ name | string | 广告组名称关键字，长度为1-30个字符，其中1个中文字符算2位 |
| └─ marketing_goal | string | 必填，广告组营销目标，允许值：<br>• VIDEO_PROM_GOODS：推商品<br>• LIVE_PROM_GOODS：推直播间 |
| └─ marketing_scene | string | 广告类型，默认为 FEED，允许值：<br>• FEED：通投广告<br>• SEARCH：搜索广告<br>• SHOPPING_MALL：商城广告 |
| └─ status | string | 广告组状态，允许值：<br>• ALL：所有包含已删除<br>• ENABLE：启用<br>• DISABLE：暂停<br>• DELETE：已删除<br>**不传入即默认返回"所有不包含已删除"** |
| page | number | 页码，默认值：1 |
| page_size | number | 页面大小，默认值: 10，允许值：10、20、50、100、500、1000 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "YOUR_ACCESS_TOKEN"
PATH = "/open_api/v1.0/qianchuan/campaign_list/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    return urlunparse(("https", "api.oceanengine.com", path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == "__main__":
    # Args in JSON format
    my_args = json.dumps({
        "advertiser_id": 123456789,
        "filter": {
            "marketing_goal": "VIDEO_PROM_GOODS",
            "marketing_scene": "FEED",
            "status": "ENABLE"
        },
        "page": 1,
        "page_size": 10
    })
    print(get(my_args))
```

## 应答字段
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | object | json返回值 |
| └─ list | object[] | 广告组列表 |
| └─ └─ id | number | 广告组ID |
| └─ └─ name | string | 广告组名称 |
| └─ └─ budget | float | 广告组预算，单位：元，精确到两位小数。 |
| └─ └─ budget_mode | string | 广告组预算类型 |
| └─ └─ marketing_goal | string | 广告组营销目标<br>• VIDEO_PROM_GOODS：推商品<br>• LIVE_PROM_GOODS：推直播间 |
| └─ └─ marketing_scene | string | 广告类型<br>• FEED：通投广告<br>• SEARCH：搜索广告<br>• SHOPPING_MALL：商城广告 |
| └─ └─ status | string | 广告组状态<br>• ALL：所有包含已删除<br>• ENABLE：启用<br>• DISABLE：暂停<br>• DELETE：已删除 |
| └─ └─ create_date | string | 广告组创建日期, 格式：yyyy-mm-dd |
| └─ page_info | object | 分页信息 |
| └─ └─ page | number | 页码 |
| └─ └─ page_size | number | 页面大小 |
| └─ └─ total_number | number | 总数 |
| └─ └─ total_page | number | 总页数 |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "list": [
      {
        "id": 123456789,
        "name": "千川推广组-商品推广",
        "budget": 100.00,
        "budget_mode": "BUDGET_MODE_INFINITE",
        "marketing_goal": "VIDEO_PROM_GOODS",
        "marketing_scene": "FEED",
        "status": "ENABLE",
        "create_date": "2024-01-01"
      },
      {
        "id": 987654321,
        "name": "千川推广组-直播间推广",
        "budget": 200.50,
        "budget_mode": "BUDGET_MODE_DAY",
        "marketing_goal": "LIVE_PROM_GOODS",
        "marketing_scene": "FEED",
        "status": "ENABLE",
        "create_date": "2024-01-02"
      }
    ],
    "page_info": {
      "page": 1,
      "page_size": 10,
      "total_number": 2,
      "total_page": 1
    },
    "request_id": "20240101123456789"
  }
}
```

## 官方文档链接
https://open.oceanengine.com/labels/12/docs/1702055567088643?origin=left_nav