# 巨量本地推 - 获取项目详情

## 接口描述
获取项目详情

**该接口暂不支持拉取推广目的为获取线索的项目**

## 请求地址
`GET https://api.oceanengine.com/open_api/v3.0/local/project/detail/`

## 请求方法
GET

## 请求Header

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| local_account_id | number | 必填 | 本地推广告账户ID |
| project_id | number | 必填 | 项目ID |

## 请求示例

```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/local/project/detail/"

def build_url(path, query=""):
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    local_account_id = *********  # 替换为实际的本地推广告账户ID
    project_id = ****************  # 替换为实际的项目ID
    my_args = "{\"local_account_id\": \"%s\", \"project_id\": \"%s\"}" % (local_account_id, project_id)
    print(get(my_args))
```

## 响应参数

| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | 返回数据 |
| project_id | number | 项目ID |
| local_account_id | number | 本地推广告账户ID |
| aweme_id | string | 抖音号ID |
| marketing_goal | string | 营销场景，枚举值：<br/>• `LIVE` - 直播<br/>• `VIDEO_IMAGE` - 短视频/图文 |
| local_delivery_scene | string | 推广目的，枚举值：<br/>• `CONTENT_HEAT` - 线上互动<br/>• `POI_RECOMMEND` - 线下到店<br/>• `PRODUCT_PAY` - 团购成交 |
| ad_type | string | 广告类型 |
| name | string | 项目名称 |
| delivery_poi_mode | string | 推广门店类型 |
| auto_update_pois | string | 自动更新门店开启状态，仅推广全部门店项目返回，枚举值：<br/>• `OFF` - 不启用<br/>• `ON` - 启用 |
| poi_id | number | 门店id（当项目推广单个门店时返回） |
| multi_poi_id | number | 多门店id（说明：当推广多门店时，仅返回多门店id，可通过【根据多门店id查门店列表】接口查询门店列表） |
| product_id | number | 商品ID |
| external_action | string | 优化目标，枚举值：<br/>• `FOLLOW_ACTION` - 粉丝增长<br/>• `LIVE_ENGAGEMENT` - 直播加热<br/>• `LIVE_OTO_CLICK` - 直播间商品点击<br/>• `LIVE_OTO_GROUP_BUYING` - 直播间团购购买<br/>• `NATIVE_ACTION` - 用户互动<br/>• `SHOW` - 展示量 |
| schedule_type | string | 投放日期类型，枚举值：<br/>• `FROM_NOW_ON` - 从今天起长期投放<br/>• `START_TO_END` - 设置开始结束时间<br/>• `FIXED_TIME` - 固定时长投放 |
| schedule_fixed_seconds | number | 固定投放时长，单位为秒 |
| start_time | string | 投放起始时间 |
| end_time | string | 投放结束时间 |
| schedule_time | string | 投放时段<br/>格式是48*7位字符串，且都是0或1。也就是以半个小时为最小粒度，周一至周日每天分为48个区段，0为不投放，1为投放，不传、全传0、全传1均代表全时段投放。<br/>例如：000000000000000000000001111000000000000000000000000000000000000000000001111000000000000000000000000000000000000000000001111000000000000000000000000000000000000000000001111000000000000000000000000000000000000000000001111000000000000000000000000000000000000000000001111000000000000000000000000000000000000000000001111000000000000000000000，则投放时段为周一到周日的11:30~13:30 |
| bid_type | string | 出价方式 |
| bid | number | 出价，单位为分 |
| budget_mode | string | 项目预算类型，枚举值：<br/>• `BUDGET_MODE_DAY` - 日预算<br/>• `BUDGET_MODE_TOTAL` - 总预算 |
| budget | number | 项目预算，单位为分 |
| is_set_peak_budget | bool | 是否设置了高峰日预算 |
| high_budget_rate | number | 上调高峰日预算比例<br/>注意：该字段为百分比，例如：传"40"表示高峰日时预算上调"40%" |
| peak_week_days | string[] | 高峰日-自然周 |
| peak_holidays | string[] | 高峰日-节假日 |
| audience | object | 定向设置 |
| ├─ district | string | 地域设置，枚举值：<br/>• `ALL` - 不限<br/>• `LOCAL` - 自定义区域<br/>• `POI` - 门店附近<br/>• `REGION` - 按行政区域 |
| ├─ region | object | 行政区域定向设置 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ city | number[] | 地域定向省市或者区县列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ city_divide | string | 城市划分 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ location_type | string | 地域类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ region_ver | string | 行政区域版本号 |
| ├─ custom_area | object[] | 自定义区域定向设置 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ name | string | 地点名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ area_radius | number | 自定义区域半径，单位为m |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ long | double | 经度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ lat | double | 纬度 |
| ├─ poi_around | object | 门店附近定向设置 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ poi_around_ids | number[] | 定向门店ids |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ poi_around_radius | string | 门店附近半径，枚举值：<br/>• `KM_6` - 门店附近6km<br/>• `KM_8` - 门店附近8km<br/>• `KM_10` - 门店附近10km<br/>• `KM_12` - 门店附近12km<br/>• `KM_15` - 门店附近15km<br/>• `KM_20` - 门店附近20km |
| ├─ gender | string | 性别，枚举值：<br/>• `FEMALE` - 女<br/>• `MALE` - 男<br/>• `NONE` - 不限 |
| ├─ age | string[] | 年龄，枚举值：<br/>5分段（age_5_part_set）：<br/>• `AGE_BETWEEN_18_19`<br/>• `AGE_BETWEEN_20_23`<br/>• `AGE_BETWEEN_24_30`<br/>• `AGE_BETWEEN_31_35`<br/>• `AGE_BETWEEN_36_40`<br/>• `AGE_BETWEEN_41_45`<br/>• `AGE_BETWEEN_46_50`<br/>• `AGE_BETWEEN_51_55`<br/>• `AGE_BETWEEN_56_59`<br/>• `AGE_ABOVE_60`<br/>10分段（age_10_part_set）：<br/>• `AGE_BETWEEN_18_23`<br/>• `AGE_BETWEEN_24_30`<br/>• `AGE_BETWEEN_31_40`<br/>• `AGE_BETWEEN_41_49`<br/>• `AGE_ABOVE_50` |
| ├─ retargeting_tags | number[] | 定向人群包列表 |
| ├─ retargeting_tags_exclude | number[] | 排除人群包列表 |
| ├─ hide_if_converted | string | 过滤已转化用户，枚举值：<br/>• `ADVERTISER` - 广告主账户<br/>• `APP` - 应用<br/>• `COMPANY` - 公司账户<br/>• `NO_EXCLUDE` - 不过滤<br/>• `ORGANIZATION` - 组织账户<br/>• `PROJECT` - 项目<br/>• `PROMOTION` - 广告 |
| ├─ converted_time_duration | string | 过滤已转化时间，枚举值：<br/>• `ONE_MONTH` - 1个月<br/>• `SEVEN_DAY` - 七天<br/>• `SIX_MONTH` - 6个月<br/>• `THREE_MONTH` - 3个月<br/>• `TODAY` - 当天<br/>• `TWELVE_MONTH` - 12个月 |
| request_id | string | 请求日志id |

## 响应示例

```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "project_id": ****************,
    "local_account_id": *********,
    "aweme_id": "abc123456",
    "marketing_goal": "VIDEO_IMAGE",
    "local_delivery_scene": "PRODUCT_PAY",
    "ad_type": "GENERAL",
    "name": "示例团购商品推广项目",
    "delivery_poi_mode": "PART",
    "auto_update_pois": "OFF",
    "poi_id": 7001,
    "multi_poi_id": null,
    "product_id": 8001,
    "external_action": "OTO_PAY",
    "schedule_type": "START_TO_END",
    "schedule_fixed_seconds": null,
    "start_time": "2023-06-01",
    "end_time": "2023-12-31",
    "schedule_time": "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111",
    "bid_type": "SMART",
    "bid": 1000,
    "budget_mode": "BUDGET_MODE_DAY",
    "budget": 50000,
    "is_set_peak_budget": true,
    "high_budget_rate": 40,
    "peak_week_days": ["SATURDAY", "SUNDAY"],
    "peak_holidays": ["SPRING_FESTIVAL", "NATIONAL_DAY"],
    "audience": {
      "district": "POI",
      "region": null,
      "custom_area": [],
      "poi_around": {
        "poi_around_ids": [7001],
        "poi_around_radius": "KM_10"
      },
      "gender": "NONE",
      "age": ["AGE_BETWEEN_18_23", "AGE_BETWEEN_24_30", "AGE_BETWEEN_31_40"],
      "retargeting_tags": [],
      "retargeting_tags_exclude": [],
      "hide_if_converted": "PROJECT",
      "converted_time_duration": "SEVEN_DAY"
    }
  },
  "request_id": "20240101*********abcdef"
}
```

## 官方文档链接
https://open.oceanengine.com/labels/37/docs/1808441520771339?origin=left_nav