# 获取千川素材库图片

## 接口描述
用于获取千川素材库中的图片素材列表。

## 请求地址
https://api.oceanengine.com/open_api/v1.0/qianchuan/creative/material/image/get/

## 请求方法
GET

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Access-Token | string | 必填，授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| advertiser_id | number | 必填，千川广告账户ID |
| filtering | object | 过滤条件 |
| └─ material_ids | string[] | 按素材ID过滤 |
| └─ image_mode | string[] | 按图片模式过滤，允许值：<br>• CREATIVE_IMAGE_MODE_LARGE：大图<br>• CREATIVE_IMAGE_MODE_SMALL：小图<br>• CREATIVE_IMAGE_MODE_GROUP：组图<br>• CREATIVE_IMAGE_MODE_VIDEO_LARGE：横版视频<br>• CREATIVE_IMAGE_MODE_VIDEO_VERTICAL：竖版视频 |
| └─ width | number | 图片宽度 |
| └─ height | number | 图片高度 |
| └─ ratio | number[] | 图片宽高比过滤 |
| └─ start_time | string | 开始时间，格式：yyyy-MM-dd |
| └─ end_time | string | 结束时间，格式：yyyy-MM-dd |
| page | number | 页码，默认值：1 |
| page_size | number | 页面大小，默认值：10，允许值：10, 20, 50, 100 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "YOUR_ACCESS_TOKEN"
PATH = "/open_api/v1.0/qianchuan/creative/material/image/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    return urlunparse(("https", "ad.oceanengine.com", path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == "__main__":
    # Args in JSON format
    my_args = "{\"advertiser_id\": ADVERTISER_ID,\"filtering\": {},\"page\": 1,\"page_size\": 10}"
    print(get(my_args))
```

## 应答字段
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | object | json返回值 |
| └─ list | object[] | 图片素材列表 |
| └─ └─ material_id | string | 素材ID |
| └─ └─ filename | string | 文件名 |
| └─ └─ image_url | string | 图片URL |
| └─ └─ signature | string | 图片签名 |
| └─ └─ width | number | 图片宽度 |
| └─ └─ height | number | 图片高度 |
| └─ └─ ratio | number | 图片宽高比 |
| └─ └─ image_mode | string | 图片模式 |
| └─ └─ format | string | 图片格式 |
| └─ └─ size | number | 文件大小，单位：字节 |
| └─ └─ create_time | string | 创建时间 |
| └─ └─ modify_time | string | 修改时间 |
| └─ └─ source | string | 素材来源 |
| └─ └─ material_tags | string[] | 素材标签 |
| └─ page_info | object | 分页信息 |
| └─ └─ page | number | 页码 |
| └─ └─ page_size | number | 页面大小 |
| └─ └─ total_number | number | 总数 |
| └─ └─ total_page | number | 总页数 |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "request_id": "202105111551080102121931483A043E53",
  "data": {
    "list": [
      {
        "material_id": "img123456789",
        "filename": "example_image.jpg",
        "image_url": "https://example.com/image.jpg",
        "signature": "abc123def456",
        "width": 1200,
        "height": 630,
        "ratio": 1.9,
        "image_mode": "CREATIVE_IMAGE_MODE_LARGE",
        "format": "jpeg",
        "size": 156780,
        "create_time": "2021-05-11 15:49:01",
        "modify_time": "2021-05-11 15:49:01",
        "source": "UPLOADED",
        "material_tags": ["产品", "促销"]
      }
    ],
    "page_info": {
      "page": 1,
      "page_size": 10,
      "total_number": 25,
      "total_page": 3
    }
  }
}
```

## 官方文档链接
https://open.oceanengine.com/labels/12/docs/1739304248623182?origin=left_nav