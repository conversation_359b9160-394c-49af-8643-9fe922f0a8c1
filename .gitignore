# ===========================================
# 前端项目忽略文件 (Vue3/React)
# ===========================================

# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建输出
dist/
build/
.next/
out/
.nuxt/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov

# nyc测试覆盖率
.nyc_output

# 依赖锁定文件 (可选，根据团队策略决定)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# 缓存目录
.cache/
.parcel-cache/
.eslintcache

# 临时文件
.tmp/
.temp/

# ===========================================
# 后端项目忽略文件 (Python)
# ===========================================

# Python字节码文件
__pycache__/
*.py[cod]
*$py.class

# C扩展
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 翻译
*.mo
*.pot

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx文档
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath解析文件
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# pytype静态类型分析器
.pytype/

# Cython调试符号
cython_debug/

# ===========================================
# 通用忽略文件
# ===========================================

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 密钥和敏感信息
*.pem
*.key
*.crt
secrets.json
config.json

# 日志文件
*.log
logs/

# 备份文件
backup/
*.backup

# 测试数据
test_data/
sample_data/
*.csv
*.xlsx
*.xls

# 文档生成
docs/build/
docs/_build/

# 性能分析
*.prof
*.profile

# 内存转储
*.dump
*.dmp

# 锁文件
*.lock
.lock

# 本地配置
local_config.py
local_settings.py
config.local.py

# 用户特定文件
user_config.py
personal_settings.py 