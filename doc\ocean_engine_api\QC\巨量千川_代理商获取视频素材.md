# 巨量千川 - 代理商获取视频素材

## 接口概述
代理商获取视频素材信息

## 请求信息

### 请求地址
```
GET https://api.oceanengine.com/open_api/v1.0/qianchuan/agent/material/video/
```

### 请求头
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Access-Token | string | 是 | 授权access_token，可以通过【获取Access token】接口获取 |

### 请求参数
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| advertiser_id | number | 是 | 广告主ID |
| filtering | object | 否 | 过滤条件 |
| video_source | string[] | 否 | 视频来源：AWEME、E_COMMERCE、LIVE_HIGHLIGHT等 |
| page | number | 否 | 页码，默认值：1 |
| page_size | number | 否 | 页面大小，允许值：10, 20, 50, 100，默认值：10 |

### 请求示例
```python
# coding=utf-8
import json
import requests
from six.moves.urllib.parse import urlencode

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/agent/material/video/"

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, str) else json.dumps(v) for k, v in args.items()})
    url = f"https://api.oceanengine.com{PATH}?{query_string}"
    headers = {"Access-Token": ACCESS_TOKEN}
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    my_args = json.dumps({
        "advertiser_id": 123456789,
        "filtering": {
            "video_source": ["AWEME", "E_COMMERCE"]
        },
        "page": 1,
        "page_size": 10
    })
    print(get(my_args))
```

## 响应信息

### 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码 |
| message | string | 返回信息 |
| data | object | 返回数据 |
| video_materials | object[] | 视频素材列表 |
| page_info | object | 分页信息 |
| request_id | string | 请求日志id |

### 响应示例
```json
{
    "code": 0,
    "message": "OK",
    "data": {
        "video_materials": [
            {
                "video_id": "v0200ff00000xxxx",
                "title": "视频标题",
                "duration": 15,
                "source": "AWEME",
                "cover_image": {
                    "web_url": "https://example.com/cover.jpg",
                    "width": 720,
                    "height": 1280
                }
            }
        ],
        "page_info": {
            "page": 1,
            "page_size": 10,
            "total_page": 1,
            "total_num": 1
        },
        "request_id": "20240101123456789"
    }
}
```

## 官方文档链接
[https://open.oceanengine.com/labels/12/docs/1827446107102592](https://open.oceanengine.com/labels/12/docs/1827446107102592)