# 巨量本地推 - 获取已授权账户

## 接口描述
此接口用于获取已经授权的账号列表，账号包含了广告主、纵横组织、代理商等角色。

> **注意：**
> - 一次授权多个账号，共用一个Access Token；一个Access Token可用于操作授权的多个账号
> - Access token与账户之间对应关系不要弄混，以免后续调用广告主相关接口报No permission错误

**重要提醒：**
- **重新授权会覆盖前一次授权，需要确保此次授权已经勾选了想要的全部账户！！** 重新授权需要重新获取access token
- **此接口可查询授权界面选择的角色账号id，包括广告主id、纵横组织id、代理商id，具体可以通过account_role来区分**
- 如果是纵横组织或者代理商，需要通过"获取工作台下账户列表"/"获取代理商下广告账户列表"获取其下的广告主

## 请求地址
```
https://api.oceanengine.com/open_api/oauth2/advertiser/get/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，从"获取Access Token"和"刷新Access Token"的返回结果中得到 |

## 请求参数
无

## 请求示例
```python
def get_authorized_advertisers():
    import requests
    
    open_api_url_prefix = "https://api.oceanengine.com/open_api/"
    uri = "oauth2/advertiser/get/"
    url = open_api_url_prefix + uri
    
    headers = {
        "Access-Token": "your_access_token"
    }
    
    rsp = requests.get(url, headers=headers)
    rsp_data = rsp.json()
    return rsp_data
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| └─ list | array | 账号列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_id | number | 账号id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ account_string_id | string | 账号id（字符串型）<br/>当advertiser_role=10有效，即抖音号类型时，即为aweme_sec_uid，可用于Dou+接口调用 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_name | string | 账号名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ is_valid | bool | 授权有效性，允许值：true/false<br/>false表示对应的user在客户中心/一站式平台代理商平台变更了对此账号的权限，需要到对应平台进行调整 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ account_role | string | 新版账号角色，见【枚举值-账户角色】 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ company_list | object[] | 代理商账户下勾选账户，但授权时选择的是代理商类型账户时，该字段才有意义<br/>company_list为空时，代表当前代理商账户下所有adv均可访问；不为空时，代表仅能访问该部分客户id下的adv |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ customer_company_id | number | 客户公司id |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ customer_company_name | string | 客户公司名 |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "list": [
      {
        "advertiser_id": **********,
        "account_string_id": "aweme_sec_uid_string",
        "advertiser_name": "本地推广告主",
        "is_valid": true,
        "account_role": "CUSTOMER_OPERATOR",
        "company_list": [
          {
            "customer_company_id": 123456,
            "customer_company_name": "客户公司A"
          }
        ]
      }
    ]
  }
}
```

## 常见问题
- [调接口时，返回"No permission to operate advertiser XXXX"的错误？](https://ad.oceanengine.com/athena/faq/index.html?plat_id=7&id=212)

## 官方文档链接
[获取已授权账户](https://open.oceanengine.com/labels/37/docs/****************?origin=left_nav)