# 巨量千川 - 删除广告计划下素材

## 接口概述
支持删除广告计划下素材，无需通过计划更新接口删除创意

## 请求信息

### 请求地址
```
POST https://api.oceanengine.com/open_api/v1.0/qianchuan/ad/material/delete/
```

### 请求头
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Access-Token | string | 是 | 授权access_token，可以通过【获取Access token】接口获取 |
| Content-Type | string | 是 | 请求消息类型，允许值：`application/json` |

### 请求参数
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| advertiser_id | number | 是 | 广告主ID |
| ad_id | number | 是 | 计划ID |
| material_ids | number[] | 是 | 待删除素材ID，注意：最大支持100个素材 |

### 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/ad/material/delete/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def post(json_str):
    # type: (str) -> dict
    """
    Send POST request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    url = build_url(PATH)
    args = json.loads(json_str)
    headers = {
        "Access-Token": ACCESS_TOKEN,
        "Content-Type": "application/json",
    }
    rsp = requests.post(url, headers=headers, json=args)
    return rsp.json()

if __name__ == '__main__':
    advertiser_id = "ADVERTISER_ID"
    ad_id = "AD_ID"
    material_ids = [123456789, 987654321]
    
    # Args in JSON format
    my_args = json.dumps({
        "advertiser_id": advertiser_id,
        "ad_id": ad_id,
        "material_ids": material_ids
    })
    
    print(post(my_args))
```

### Curl示例
```bash
curl -X POST \
  'https://api.oceanengine.com/open_api/v1.0/qianchuan/ad/material/delete/' \
  -H 'Access-Token: xxx' \
  -H 'Content-Type: application/json' \
  -d '{
    "advertiser_id": 123456789,
    "ad_id": 987654321,
    "material_ids": [123456789, 987654321]
  }'
```

## 响应信息

### 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | json返回值 |
| request_id | string | 请求日志id |

### 响应示例
```json
{
    "code": 0,
    "message": "OK",
    "data": {},
    "request_id": "20240101123456789"
}
```

## 注意事项
- 最大支持一次删除100个素材
- 删除操作不可逆，请谨慎操作
- 删除素材后，关联的创意也会受到影响

## 官方文档链接
[https://open.oceanengine.com/labels/12/docs/1803706707580163](https://open.oceanengine.com/labels/12/docs/1803706707580163)