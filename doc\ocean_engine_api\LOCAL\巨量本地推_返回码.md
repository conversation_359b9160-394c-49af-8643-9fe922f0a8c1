# 巨量本地推 - 返回码

## 接口描述
巨量本地推平台使用与巨量广告平台相同的返回码体系。本文档说明返回码的使用方式和参考来源。

## 返回码说明

巨量本地推平台的API接口返回码与巨量广告平台**完全一致**，包括：

- 成功返回码
- 客户端错误返回码
- 服务端错误返回码
- 业务逻辑错误码
- 权限相关错误码

## 使用方式

### 1. 参考文档
请直接参考巨量广告平台的返回码文档：
- 文档位置：`docs/api/AD/巨量广告_返回码.md`
- 官方文档：[巨量广告返回码](https://open.oceanengine.com/labels/7/docs/1696710760866831?origin=left_nav)

### 2. 通用返回格式
所有API接口返回的数据都遵循统一的JSON格式：

```json
{
  "code": 0,
  "message": "OK",
  "data": {
    // 具体的返回数据
  },
  "request_id": "202312345678901234567890"
}
```

### 3. 常见返回码示例

| 返回码 | 描述 | 处理建议 |
|-------|------|----------|
| 0 | 请求成功 | 正常处理返回数据 |
| 40000 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 40100 | 未授权访问 | 检查Access Token是否有效 |
| 40102 | Access Token过期 | 使用Refresh Token刷新 |
| 50000 | 服务器内部错误 | 稍后重试或联系技术支持 |

## 错误处理建议

1. **统一错误处理**：使用与巨量广告平台相同的错误处理逻辑
2. **日志记录**：记录完整的错误信息，包括code、message和request_id
3. **重试机制**：对于临时性错误（5xx系列），实施指数退避重试
4. **用户友好提示**：将技术错误码转换为用户易懂的提示信息

## 重要说明

- 本地推平台与广告平台**共享相同的错误码定义**
- 错误处理逻辑可以复用
- 新增的错误码会同步更新到广告平台文档中

## 官方文档链接
由于本地推使用广告平台的返回码，请参考：
[巨量广告返回码](https://open.oceanengine.com/labels/7/docs/1696710760866831?origin=left_nav)