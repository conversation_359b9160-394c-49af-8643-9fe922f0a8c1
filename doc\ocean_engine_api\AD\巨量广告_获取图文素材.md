# 巨量广告 - 获取图文素材

## 接口描述
通过此接口，用户可以获取经过一定条件过滤后的广告主下创意素材库下图文及图文信息。

**注意：** page*page_size＞10000时会报错，请注意调增请求量级

## 请求地址
```
GET https://api.oceanengine.com/open_api/2/carousel/list/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主id |
| filtering | object | 可选 | 过滤器 |
| └─ carousel_ids | number[] | 可选 | 图文id |
| └─ file_name | string | 可选 | 图文名称 |
| └─ image_ids | string[] | 可选 | 图文内图片id筛选 |
| └─ audio_id | string | 可选 | 图文内音频id筛选 |
| └─ start_time | string | 可选 | 根据图文上传时间进行过滤的起始时间，与end_time搭配使用。格式："yyyy-mm-dd HH:MM:SS" |
| └─ end_time | string | 可选 | 根据图文上传时间进行过滤的结束时间，与start_time搭配使用。格式："yyyy-mm-dd HH:MM:SS" |
| └─ source | string[] | 可选 | 图文素材来源，可选值：<br/>• `AD_SITE` 本地上传<br/>• `BP` 组织共享<br/>• `ACCOUNT_PUSH` 账户推送<br/>• `AIC` 即创<br/>• `OPEN_API` MarketingAPI<br/>• `CEWEBRITY_CAROUSEL` 抖音主页 |
| page | number | 可选 | 页码，默认值：`1`，注意：page*page_size＞10000时会报错 |
| page_size | number | 可选 | 页面大小，默认值：`20`，注意：page*page_size＞10000时会报错 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/2/carousel/list/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    advertiser_id = ADVERTISER_ID
    carousel_ids_list = CAROUSEL_IDS
    carousel_ids = json.dumps(carousel_ids_list)
    file_name = FILE_NAME
    image_ids_list = IMAGE_IDS
    image_ids = json.dumps(image_ids_list)
    audio_id = AUDIO_ID
    start_time = START_TIME
    end_time = END_TIME
    page_size = PAGE_SIZE
    page = PAGE

    # Args in JSON format
    my_args = "{\"advertiser_id\": \"%s\", \"filtering\": {\"carousel_ids\": %s, \"file_name\": \"%s\", \"image_ids\": %s, \"audio_id\": \"%s\", \"start_time\": \"%s\", \"end_time\": \"%s\"}, \"page_size\": \"%s\", \"page\": \"%s\"}" % (advertiser_id, carousel_ids, file_name, image_ids, audio_id, start_time, end_time, page_size, page)
    print(get(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ carousels | object[] | 账户下图文素材列表，对应素材中心-图文库列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ id | number | 图文id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ images | object[] | 图文内图片信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ image_id | string | 图片id |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ material_id | number | 素材id |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ height | number | 高度 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ width | number | 宽度 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ ratio | float | 图片比例 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ url | string | 图片预览url |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ audio | object | 图文内音频信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ audio_id | string | 音频id |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ audio_url | string | 音频播放地址 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ file_name | string | 图文标题 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ create_time | string | 图文创建时间，格式："yyyy-mm-dd HH:MM:SS" |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ update_time | string | 图文上传时间，格式："yyyy-mm-dd HH:MM:SS" |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ source | string | 图文素材来源 |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 页码 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_count | number | 总页数 |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {}
}
```

## 官方文档链接
[获取图文素材](https://open.oceanengine.com/labels/7/docs/1773554026740736?origin=left_nav)