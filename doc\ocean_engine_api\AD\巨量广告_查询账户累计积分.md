# 巨量广告 - 查询账户累计积分

## 接口描述
账户累计积分

## 请求地址
```
GET https://api.oceanengine.com/open_api/v3.0/security/score_total/get/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主ID |
| page | number | 可选 | 页数，默认值：`1`，page范围为[1,99999] |
| page_size | number | 可选 | 页面大小，默认值：`10`，page_size范围为[1,100] |
| business_line | string | 必填 | 业务线，允许值：<br/>• `AD` 巨量广告<br/>• `LOCALAD` 巨量本地推 |
| filtering | object | 可选 | 过滤器 |
| └─ year | string | 可选 | 积分年度，如：2000 |
| └─ illegal_type | string | 可选 | 违规类型，可选值：<br/>• `GENERAL` 一般违规（AD）<br/>• `SERIOUS` 严重违规（AD）<br/>• `MINOR` 一般违规（本地推）<br/>• `CRITICAL` 严重违规（本地推） |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/security/score_total/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    advertiser_id = ADVERTISER_ID
    page = PAGE
    page_size = PAGE_SIZE
    business_line = BUSINESS_LINE
    year = YEAR
    illegal_type = ILLEGAL_TYPE

    # Args in JSON format
    my_args = "{\"advertiser_id\": \"%s\", \"page\": \"%s\", \"page_size\": \"%s\", \"business_line\": \"%s\", \"filtering\": {\"year\": \"%s\", \"illegal_type\": \"%s\"}}" % (advertiser_id, page, page_size, business_line, year, illegal_type)
    print(get(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 页数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total | number | 总数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| ├─ score_info_list | object[] | 积分详情 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_id | number | 广告主id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ illegal_type | string | 违规类型，可选值：<br/>• `GENERAL` 一般违规（AD）<br/>• `SERIOUS` 严重违规（AD）<br/>• `MINOR` 一般违规（本地推）<br/>• `CRITICAL` 严重违规（本地推） |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ year | string | 积分年度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ score | number | 年分 |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {}
}
```

## 官方文档链接
[查询账户累计积分](https://open.oceanengine.com/labels/7/docs/1807434247414986?origin=left_nav)