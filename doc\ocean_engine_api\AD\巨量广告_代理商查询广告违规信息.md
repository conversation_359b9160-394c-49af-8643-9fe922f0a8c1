# 巨量广告 - 【代理商】查询广告违规信息

## 接口描述
**通过此接口，用户可以获取代理商账户下：**
- 支持获取在投放中图片、视频和落地页被拒审的巨量广告信息，仅展示广告拒审时的信息
- 支持获取广告中未过审的素材信息以及这个素材还在同代理商的哪些广告下（只披露近7天有消耗的关联广告）

## 请求地址
```
GET https://api.oceanengine.com/open_api/2/agent/query/risk_promotion_list/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| agent_id | number | 必填 | 代理商账户ID |
| business_type | string | 必填 | 业务线<br/>• `AD` 巨量广告（默认值），获取巨量广告账户下的违规广告信息 |
| start_date | string | 必填 | 推送开始时间，支持格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss，如果是 yyyy-MM-dd，默认 yyyy-MM-dd 00:00:00<br/>比如上传：2024-03-01，默认转换查询2024-03-01 00:00:00 |
| end_date | string | 必填 | 推送结束时间，支持格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss，如果是 yyyy-MM-dd，默认 yyyy-MM-dd 23:59:59<br/>比如上传：2024-03-01，默认转换查询2024-03-01 23:59:59（最长跨度31天） |
| cursor | number | 可选 | 页码游标值，初始从Long.MAX开始，后续传入返回的cursor值，不传值相当于page=1，查询count条数据 |
| count | number | 可选 | 页码游标值，最大支持500 |
| filtering | object | 可选 | 过滤器 |
| └─ promotion_ids | number[] | 可选 | 广告ID，最多支持100个 |
| └─ promotion_name | string | 可选 | 广告名称，模糊搜索，长度不能超过30 |
| └─ promotion_status | string | 可选 | 广告状态，可选值：详见官方文档完整枚举列表 |
| └─ illegal_material_ids | number[] | 可选 | 违规素材ids，最多支持100个 |
| └─ advertiser_ids | number[] | 可选 | 广告主账户ID，最多支持100个 |
| └─ advertiser_name | string | 可选 | 广告主账户名称，模糊搜索，长度不能超过30 |
| └─ final_operator_tag | string | 可选 | 自走收综合标签（T+1后数据稳定），可选值：<br/>• `DECREASE_QUANTITY` 走量<br/>• `EMPTY` 无标签<br/>• `INCREASE_QUANTITY` 收量<br/>• `SELF_OPERATION` 自运营 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/2/agent/query/risk_promotion_list/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    agent_id = AGENT_ID
    business_type = BUSINESS_TYPE
    start_date = START_DATE
    end_date = END_DATE
    cursor = CURSOR
    count = COUNT
    promotion_ids_list = PROMOTION_IDS
    promotion_ids = json.dumps(promotion_ids_list)
    promotion_name = PROMOTION_NAME
    promotion_status = PROMOTION_STATUS
    illegal_material_ids_list = ILLEGAL_MATERIAL_IDS
    illegal_material_ids = json.dumps(illegal_material_ids_list)
    advertiser_ids_list = ADVERTISER_IDS
    advertiser_ids = json.dumps(advertiser_ids_list)
    advertiser_name = ADVERTISER_NAME
    final_operator_tag = FINAL_OPERATOR_TAG

    # Args in JSON format
    my_args = "{\"agent_id\": \"%s\", \"business_type\": \"%s\", \"start_date\": \"%s\", \"end_date\": \"%s\", \"cursor\": \"%s\", \"count\": \"%s\", \"filtering\": {\"promotion_ids\": %s, \"promotion_name\": \"%s\", \"promotion_status\": \"%s\", \"illegal_material_ids\": %s, \"advertiser_ids\": %s, \"advertiser_name\": \"%s\", \"final_operator_tag\": \"%s\"}}" % (agent_id, business_type, start_date, end_date, cursor, count, promotion_ids, promotion_name, promotion_status, illegal_material_ids, advertiser_ids, advertiser_name, final_operator_tag)
    print(get(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ data | object[] | 违规广告列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_id | number | 广告ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_name | string | 广告名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_status | string | 广告状态，详见请求参数promotion_status枚举 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ material_list | object[] | 违规素材列表，包含广告下投前+投中拒审的素材信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ id | number | 素材ID（落地页站点ID） |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ type | string | 素材类型：IMAGE/VIDEO/SITE |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ risk_content | string[] | 素材违规原因 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ ref_promotion_ids | number[] | 同代理商账户下的其他关联广告ID |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ ref_promotion_list | object[] | 同代理商账户下的其他关联广告信息（只披露近七天有投放消耗的关联广告） |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_id | number | 广告主id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_name | string | 广告主账户名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ company_id | number | 广告主公司ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ company_name | string | 广告主公司名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ agent_id | number | 代理商账户ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ agent_name | string | 代理商账户名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ business_type | string | 业务线，同入参business_type枚举 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ final_operator_tag | string | 自走收综合标签，同入参final_operator_tag枚举 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ send_time | string | 推送时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ id | number | 记录的唯一ID |
| ├─ cursor_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ cursor | number | 页码游标值 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ count | number | 页码游标值 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ has_more | bool | 是否有下一页 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {}
}
```

## 官方文档链接
[【代理商】查询广告违规信息](https://open.oceanengine.com/labels/7/docs/1790052406659072?origin=left_nav)