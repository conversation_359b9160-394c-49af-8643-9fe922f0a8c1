# 获取代理商账户关联的广告账户列表

## 接口描述
获取代理商下的广告主ID列表。

**注意：** 当请求数据量超过10000时，请使用cursor+count的分页方式请求数据

## 请求地址
https://api.oceanengine.com/open_api/2/agent/advertiser/select/

## 请求方法
GET

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Access-Token | string | 必填，授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| advertiser_id | number | 必填，代理商ID |
| company_ids | number[] | 客户id列表，可用来过滤某个客户的adv |
| permission | string[] | 查询账号额外权限，允许值：QC_AWEME 千川随心推；传入则返回带随心推权限标记的 Adv 列表 |
| filtering | object | 过滤条件 |
| └─ stat_cost_fen_gt | number | 消耗统计周期内的消耗金额，单位分。注意：支持过滤数据统计周期内消耗大于xx的adv |
| └─ cost_period | string | 消耗统计周期，允许值：<br>• TODAY：今天<br>• YESTERDAY：昨天<br>• LAST_7_DAYS：最近7天，不包含当天<br>• LAST_15_DAYS：最近15天，不包含当天<br>• LAST_30_DAYS：最近30天，不包含当天 |
| └─ advertiser_ids | number[] | 需要过滤广告主id列表，最多支持500个adv |
| └─ create_start_time | string | 账户创建时间筛选的起始时间，精确到秒，格式yyyy-MM-dd HH:mm:ss |
| └─ create_end_time | string | 账户创建时间筛选的结束时间，精确到秒，格式yyyy-MM-dd HH:mm:ss |
| └─ sale_id | number | 销售id |
| └─ sale_name | string | 销售姓名 |
| └─ is_exist_sale | bool | 是否存在销售，不传不过滤<br>true:返回有销售的记录<br>false:返回没有销售的记录 |
| page | string | 页码.默认值: 1<br>注：page+page_size与cursor+count为两种分页方式<br>page+page_size适用于获取数据记录数<10000的场景 |
| page_size | string | 页面数据量.默认值: 100<br>注：page+page_size与cursor+count为两种分页方式<br>page+page_size适用于获取数据记录数<10000的场景，page_size最大值为：1000 |
| cursor | string | 页码游标值，第一次拉取，无需入参<br>注：page+page_size与cursor+count为两种分页方式<br>cursor+count适用于获取数据记录数≥10000的场景 |
| count | string | 页面数据量，页面数据量<br>注：page+page_size与cursor+count为两种分页方式<br>cursor+count适用于获取数据记录数≥10000的场景 |

## 请求示例
```python
def get_advertiser():
    import requests
    open_api_url_prefix = "https://api.oceanengine.com/open_api/"
    uri = "2/agent/advertiser/select/"
    url = open_api_url_prefix + uri
    params = {
        "page": 1,
        "page_size": 100,
    }
    headers = {"Access-Token": "xxx"}
    rsp = requests.get(url, json=params, headers=headers)
    rsp_data = rsp.json()
    return rsp_data
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | object | json返回值 |
| └─ list | number[] | 千川广告主账户ID列表 |
| └─ account_source | string | 账号列表的账号类型<br>QIANCHUAN 千川 |
| └─ page_info | object | 分页信息 |
| └─ └─ page | number | 页数 |
| └─ └─ page_size | number | 页面大小 |
| └─ └─ total_number | number | 总数 |
| └─ └─ total_page | number | 总页数 |
| └─ cursor_page_info | json | 分页信息 |
| └─ └─ total_number | number | 总数 |
| └─ └─ has_more | number | 是否有下一页 |
| └─ └─ count | number | 页面数据量 |
| └─ └─ cursor | number | 下一次分页拉取的游标值 |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "list": [
      {
        "advertiser_id": *********,
        "account_source": "QIANCHUAN",
        "qianchuan_aweme_permission": true
      },
      {
        "advertiser_id": *********,
        "account_source": "QIANCHUAN",
        "qianchuan_aweme_permission": false
      }
    ],
    "page_info": {
      "page": 1,
      "page_size": 100,
      "total_number": 2,
      "total_page": 1
    },
    "request_id": "*****************"
  }
}
```

## 官方文档链接
https://open.oceanengine.com/labels/12/docs/****************?origin=left_nav