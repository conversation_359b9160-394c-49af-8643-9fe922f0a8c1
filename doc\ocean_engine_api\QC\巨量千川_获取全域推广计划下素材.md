# 巨量千川 - 获取全域推广计划下素材

## 接口概述
获取全域推广计划下素材信息

## 请求信息

### 请求地址
```
GET https://api.oceanengine.com/open_api/v1.0/qianchuan/uni_promotion/ad/material/get/
```

### 请求头
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Access-Token | string | 是 | 授权access_token，可以通过【获取Access token】接口获取 |

### 请求参数
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| advertiser_id | number | 是 | 广告主ID |
| ad_id | number | 是 | 全域推广计划ID |
| filtering | object | 是 | 过滤条件 |
| material_type | string | 是 | 素材类型：IMAGE、VIDEO、TITLE、LIVE_ROOM |
| page | number | 否 | 页码，默认值：1 |
| page_size | number | 否 | 页面大小，允许值：10, 20, 50, 100，默认值：10 |

### 请求示例
```python
# coding=utf-8
import json
import requests
from six.moves.urllib.parse import urlencode

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/uni_promotion/ad/material/get/"

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, str) else json.dumps(v) for k, v in args.items()})
    url = f"https://api.oceanengine.com{PATH}?{query_string}"
    headers = {"Access-Token": ACCESS_TOKEN}
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    my_args = json.dumps({
        "advertiser_id": 123456789,
        "ad_id": 987654321,
        "filtering": {
            "material_type": "VIDEO"
        },
        "page": 1,
        "page_size": 10
    })
    print(get(my_args))
```

## 响应信息

### 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码 |
| message | string | 返回信息 |
| data | object | 返回数据 |
| ad_material_infos | object[] | 素材信息列表 |
| page_info | object | 分页信息 |
| request_id | string | 请求日志id |

### 响应示例
```json
{
    "code": 0,
    "message": "OK",
    "data": {
        "ad_material_infos": [
            {
                "material_type": "VIDEO",
                "material_id": 123456789,
                "audit_status": "PASS"
            }
        ],
        "page_info": {
            "page": 1,
            "page_size": 10,
            "total_page": 1,
            "total_num": 1
        },
        "request_id": "20240101123456789"
    }
}
```

## 官方文档链接
[https://open.oceanengine.com/labels/12/docs/1804363488115850](https://open.oceanengine.com/labels/12/docs/1804363488115850)