# 巨量本地推 - 获取Access Token

## 接口描述
Access-Token是调用授权关系接口的调用凭证，用于服务端对API请求鉴权。所有接口均通过请求参数中传递的 Access_Token来进行身份认证和鉴权。

> **注意：**
> - Access-Token是调用接口时，操作指定广告账户的身份凭证，有效期为24小时
> - Refresh-Token用于生成新access_token和refresh_token并且刷新时效达到续期的目的

## 请求地址
```
https://ad.oceanengine.com/open_api/oauth2/access_token/
```

## 请求方法
**POST**

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Content-Type | string | 请求消息类型，允许值：application/json |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| app_id | number | 必填 | 开发者申请的应用APP_ID，可通过【应用管理】界面查看 |
| secret | string | 必填 | 开发者应用的私钥Secret，可通过【应用管理】界面查看<br/>• 传入app_id与secret需对应，及同一应用下信息 |
| grant_type | string | 必填 | 授权类型，必须为auth_code |
| auth_code | string | 必填 | 授权码，在授权完成后回调时会提供该授权码，只有10分钟有效期，且只能使用一次，获取详情可见OAuth2.0授权 |

## 请求示例
```python
import requests

open_api_url_prefix = "https://ad.oceanengine.com/open_api/"
uri = "oauth2/access_token/"
url = open_api_url_prefix + uri
data = {
    "app_id": 123456789,
    "secret": "your_app_secret",
    "grant_type": "auth_code",
    "auth_code": "your_auth_code"
}
headers = {
    "Content-Type": "application/json"
}
rsp = requests.post(url, json=data, headers=headers)
rsp_data = rsp.json()
return rsp_data
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ access_token | string | 用于验证权限的token |
| ├─ expires_in | number | access_token剩余有效时间,单位(秒) |
| ├─ refresh_token | string | 刷新access_token,用于获取新的access_token和refresh_token，并且刷新过期时间 |
| ├─ refresh_token_expires_in | number | refresh_token剩余有效时间,单位(秒) |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "access_token": "ACCESS_TOKEN_STRING",
    "expires_in": 86400,
    "refresh_token": "REFRESH_TOKEN_STRING",
    "refresh_token_expires_in": 604800,
    "request_id": "20240712123456789abcdef"
  }
}
```

## 官方文档链接
[获取Access Token](https://open.oceanengine.com/labels/37/docs/1696710505596940?origin=left_nav)