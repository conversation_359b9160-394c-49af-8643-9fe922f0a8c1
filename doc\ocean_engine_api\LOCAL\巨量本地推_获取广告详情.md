# 巨量本地推 - 获取广告详情

## 接口描述
获取广告详情

**暂不支持拉取推广目的为获取线索的广告**

## 请求地址
`GET https://api.oceanengine.com/open_api/v3.0/local/promotion/detail/`

## 请求方法
GET

## 请求Header

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| local_account_id | number | 必填 | 本地推广告账户ID |
| promotion_id | number | 必填 | 广告ID |

## 请求示例

```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/local/promotion/detail/"

def build_url(path, query=""):
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    local_account_id = *********  # 替换为实际的本地推广告账户ID
    promotion_id = ***************  # 替换为实际的广告ID
    my_args = "{\"local_account_id\": \"%s\", \"promotion_id\": \"%s\"}" % (local_account_id, promotion_id)
    print(get(my_args))
```

## 响应参数

| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | 返回数据 |
| promotion_id | number | 广告ID |
| enable_graphic_delivery | bool | 是否开启团购卡 |
| aweme_id | string | 抖音号 |
| video_hp_visibility | string | 抖音主页可见性，枚举值：<br/>• `ALWAYS_VISIBLE` - 抖音主页可见<br/>• `HIDE_VIDEO_ON_HP` - 抖音主页单次可见 |
| live_material_type | string | 直播素材类型，枚举值：<br/>• `LIVE` - 直播素材<br/>• `VIDEO` - 广告素材 |
| customer_material_list | object[] | 自定义素材组合 |
| ├─ image_mode | string | 素材类型，枚举值：<br/>• `IMAGE_MODE_VIDEO` - 横版视频<br/>• `IMAGE_MODE_VIDEO_VERTICAL` - 竖版视频 |
| ├─ title_material | object | 标题 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ title | string | 标题内容 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ lego_material_id | number | 标题素材库id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ material_id | number | 标题素材id |
| ├─ video_material | object | 视频 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ video_id | string | 视频id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ lego_material_id | number | 视频素材库id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ material_id | number | 视频素材id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ aweme_item_id | number | 抖音主页视频ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ image_mode | string | 素材类型，枚举值：<br/>• `IMAGE_MODE_VIDEO` - 横版视频<br/>• `IMAGE_MODE_VIDEO_VERTICAL` - 竖版视频 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ video_duration | number | 视频长度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ video_height | number | 视频高度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ video_width | number | 视频宽度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ video_play_url | string | 视频播放链接 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ cover_image_height | number | 封面图片高度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ cover_image_width | number | 封面图片宽度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ cover_web_uri | string | 封面图片uri |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ cover_web_url | string | 封面图片链接 |
| request_id | string | 请求日志id |

## 响应示例

```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "promotion_id": ***************,
    "enable_graphic_delivery": true,
    "aweme_id": "abc123456",
    "video_hp_visibility": "ALWAYS_VISIBLE",
    "live_material_type": "VIDEO",
    "customer_material_list": [
      {
        "image_mode": "IMAGE_MODE_VIDEO_VERTICAL",
        "title_material": {
          "title": "优质商品推广标题",
          "lego_material_id": 1001,
          "material_id": 2001
        },
        "video_material": {
          "video_id": "v*********0*********0",
          "lego_material_id": 1002,
          "material_id": 2002,
          "aweme_item_id": 7*********012345678,
          "image_mode": "IMAGE_MODE_VIDEO_VERTICAL",
          "video_duration": 15000,
          "video_height": 1920,
          "video_width": 1080,
          "video_play_url": "https://example.com/video.mp4",
          "cover_image_height": 1920,
          "cover_image_width": 1080,
          "cover_web_uri": "cover123456",
          "cover_web_url": "https://example.com/cover.jpg"
        }
      }
    ]
  },
  "request_id": "20240101*********abcdef"
}
```

## 官方文档链接
https://open.oceanengine.com/labels/37/docs/1808442943397963?origin=left_nav