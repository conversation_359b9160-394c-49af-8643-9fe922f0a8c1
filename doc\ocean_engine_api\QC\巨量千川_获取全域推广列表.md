# 巨量千川 - 获取全域推广列表

## 接口概述
获取全域推广列表

## 请求信息

### 请求地址
```
GET https://api.oceanengine.com/open_api/v1.0/qianchuan/uni_promotion/list/
```

### 请求头
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Access-Token | string | 是 | 授权access_token，可以通过【获取Access token】接口获取 |

### 请求参数
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| advertiser_id | number | 是 | 广告主id |
| start_time | string | 是 | 开始时间，格式：2021-04-05 00:00:00 |
| end_time | string | 是 | 结束时间，格式：2021-04-06 00:00:00 |
| marketing_goal | string | 是 | 按营销目标过滤：<br>- `LIVE_PROM_GOODS`：直播带货<br>- `VIDEO_PROM_GOODS`：推商品 |
| filtering | object | 否 | 过滤条件 |
| smart_bid_type | string | 否 | 投放方式：<br>- `SMART_BID_CONSERVATIVE`：放量投放<br>- `SMART_BID_CUSTOM`：控成本投放（默认值） |
| search_keyword | string | 否 | 搜索关键词，与search_keyword_type搭配使用<br>注意：仅当marketing_goal=VIDEO_PROM_GOODS支持 |
| search_keyword_type | string | 否 | 搜索关键词类型：<br>- `PRODUCT`：商品<br>- `AWEME`：抖音号<br>- `AD`：计划（默认） |
| status | string | 否 | 计划状态：<br>- `ALL`：所有，不包括已删除<br>- `ALL_INCLUDE_DELETED`：所有,包含已删除<br>- `AUDIT`：新建审核中<br>- `DELETED`：已删除<br>- `DELIVERY_OK`：投放中<br>- `DISABLE`：已暂停<br>- `EXTERNAL_URL_DISABLE`：落地页暂不可用<br>- `FROZEN`：已终止<br>- `LIVE_ROOM_OFF`：关联直播间未开播<br>- `NO_SCHEDULE`：不在投放时段<br>- `OFFLINE_AUDIT`：审核不通过<br>- `OFFLINE_BALANCE`：账户余额不足<br>- `OFFLINE_BUDGET`：广告超出预算<br>- `QUOTA_DISABLE`：quota暂停<br>- `REAUDIT`：修改审核中<br>- `ROI2_DISABLE`：全域推广暂停<br>- `SYSTEM_DISABLE`：系统暂停<br>- `TIME_DONE`：已完成<br>- `TIME_NO_REACH`：未达到投放时间<br>- `ADVERTISER_OFFLINE_BUDGET`：账户预算不足<br>注意：仅当marketing_goal=VIDEO_PROM_GOODS支持 |
| having_cost | string | 否 | 消耗情况：<br>- `ALL`：全部<br>- `"YES"`：有消耗<br>- `"NO"`：无消耗<br>注意：仅当marketing_goal=VIDEO_PROM_GOODS支持 |
| create_start_date | string | 否 | 计划创建开始日期，时间格式 YYYY-MM-DD<br>注意：仅当marketing_goal=VIDEO_PROM_GOODS支持 |
| create_end_date | string | 否 | 计划创建结束日期，时间格式 YYYY-MM-DD<br>注意：仅当marketing_goal=VIDEO_PROM_GOODS支持 |
| need_compensate_info | bool | 否 | 是否获取成本保障信息，默认不获取<br>- false：不获取<br>- true：获取 |
| fields | string[] | 是 | 需要查询的消耗指标，见返回参数 |
| order_type | string | 否 | 排序方式：<br>- `ASC`：升序（默认）<br>- `DESC`：降序 |
| order_field | string | 否 | 排序字段，默认create_time，同时支持根据消耗指标排序：<br>- `create_time`<br>- `stat_cost`<br>- `total_cost_per_pay_order_for_roi2`<br>- `total_pay_order_count_for_roi2`<br>- `total_pay_order_gmv_for_roi2`<br>- `total_prepay_and_pay_order_roi2`<br>- `total_prepay_order_count_for_roi2`<br>- `total_ecom_platform_subsidy_amount_for_roi2`<br>- `total_pay_order_gmv_include_coupon_for_roi2` |
| page | number | 否 | 页码，默认值：1 |
| page_size | number | 否 | 页面大小，允许值：10, 20, 50, 100，默认值：10 |

### 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/uni_promotion/list/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    advertiser_id = "ADVERTISER_ID"
    start_time = "2024-01-01 00:00:00"
    end_time = "2024-01-31 23:59:59"
    marketing_goal = "LIVE_PROM_GOODS"
    fields = ["stat_cost", "total_prepay_and_pay_order_roi2", "total_pay_order_gmv_for_roi2"]
    
    # Args in JSON format
    my_args = json.dumps({
        "advertiser_id": advertiser_id,
        "start_time": start_time,
        "end_time": end_time,
        "marketing_goal": marketing_goal,
        "fields": fields,
        "order_type": "DESC",
        "order_field": "stat_cost",
        "page": 1,
        "page_size": 10
    })
    
    print(get(my_args))
```

## 响应信息

### 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | json返回值 |
| ad_list | object[] | 全域推广列表 |
| ad_info | object | 广告信息 |
| id | number | 推广id |
| name | string | 推广名称 |
| start_time | string | 当前周期开始时间，用来返回周期内数据 |
| end_time | string | 当前周期结束时间 |
| modify_time | string | 修改时间 |
| create_time | string | 创建时间 |
| marketing_goal | string | 营销目标 |
| roi2_goal | double | 支付ROI目标，最多支持两位小数 |
| budget_mode | string | 预算类型 |
| budget | double | 预算 |
| status | string | 投放状态 |
| opt_status | string | 操作状态，详见【附录-枚举值】 |
| delivery_seconds | number | 投放时长 |
| smart_bid_type | string | 投放方式：<br>- `SMART_BID_CONSERVATIVE`：放量投放<br>- `SMART_BID_CUSTOM`：控成本投放 |
| daily_delivery_time | double | 每日投放时长，0.5～24h |
| compensate_info | object | 成本保障信息 |
| status | string | 当前请求是否成功：<br>- `SUCCESS`：查询成功<br>- `FAILED`：查询失败 |
| compensate_status | string | 计划成本保障状态：<br>- `IN_EFFECT`：成本保障生效中<br>- `INVALID`：成本保障已失效<br>- `CONFIRMING`：成本保障确认中<br>- `PAID`：成本保障已到账<br>- `ENDED`：成本保障已结束<br>- `DEFAULT`：无成本保障状态 |
| reason | string | 成本保障失效/结束原因 |
| room_info | object[] | 主播信息 |
| anchor_id | string | 主播ID |
| anchor_name | string | 主播名称 |
| anchor_avatar | string | 主播头像 |
| stats_info | object | 消耗指标 |
| stat_cost | float | 整体消耗，千分之一分 |
| total_prepay_and_pay_order_roi2 | float | 整体支付ROI |
| total_pay_order_gmv_for_roi2 | float | 用户实际支付金额 |
| total_pay_order_count_for_roi2 | float | 整体成交订单数 |
| total_cost_per_pay_order_for_roi2 | float | 整体成交订单成本 |
| total_prepay_order_count_for_roi2 | number | 整体预售订单数 |
| total_prepay_order_gmv_for_roi2 | float | 整体预售订单金额 |
| total_pay_order_coupon_amount_for_roi2 | float | 整体成交智能优惠券金额 |
| total_unfinished_estimate_order_gmv_for_roi2 | float | 整体未完结预售订单预估金额 |
| total_ecom_platform_subsidy_amount_for_roi2 | float | 电商平台补贴金额 |
| total_pay_order_gmv_include_coupon_for_roi2 | float | 整体成交金额 |
| product_info | object[] | 商品信息 |
| product_id | number | 商品ID |
| product_name | string | 商品名称 |
| product_image | string | 商品预览图 |
| recommend_reasons | string[] | 推荐理由 |
| page_info | object | 分页信息 |
| page | number | 页码 |
| page_size | number | 页面大小 |
| total_page | number | 总页数 |
| total_num | number | 总数 |
| request_id | string | 请求日志id |

### 响应示例
```json
{
    "code": 0,
    "message": "OK",
    "data": {
        "ad_list": [
            {
                "ad_info": {
                    "id": 123456789,
                    "name": "全域推广计划001",
                    "start_time": "2024-01-01 00:00:00",
                    "end_time": "2024-01-31 23:59:59",
                    "modify_time": "2024-01-15 10:30:00",
                    "create_time": "2024-01-01 09:00:00",
                    "marketing_goal": "LIVE_PROM_GOODS",
                    "roi2_goal": 3.5,
                    "budget_mode": "INFINITE",
                    "budget": 10000.0,
                    "status": "DELIVERY_OK",
                    "opt_status": "ENABLE",
                    "delivery_seconds": 86400,
                    "smart_bid_type": "SMART_BID_CUSTOM",
                    "daily_delivery_time": 24.0
                },
                "compensate_info": {
                    "status": "SUCCESS",
                    "compensate_status": "IN_EFFECT",
                    "reason": ""
                },
                "room_info": [
                    {
                        "anchor_id": "987654321",
                        "anchor_name": "主播名称",
                        "anchor_avatar": "https://example.com/avatar.jpg"
                    }
                ],
                "stats_info": {
                    "stat_cost": 5000.0,
                    "total_prepay_and_pay_order_roi2": 4.2,
                    "total_pay_order_gmv_for_roi2": 21000.0,
                    "total_pay_order_count_for_roi2": 100.0,
                    "total_cost_per_pay_order_for_roi2": 50.0
                },
                "product_info": [
                    {
                        "product_id": 111222333,
                        "product_name": "商品名称",
                        "product_image": "https://example.com/product.jpg",
                        "recommend_reasons": ["热销商品", "好评如潮"]
                    }
                ]
            }
        ],
        "page_info": {
            "page": 1,
            "page_size": 10,
            "total_page": 1,
            "total_num": 1
        },
        "request_id": "20240101123456789"
    }
}
```

## 注意事项
- 时间格式必须为：YYYY-MM-DD HH:mm:ss
- 部分筛选参数仅在特定营销目标下支持
- 支持按多种消耗指标进行排序
- 成本保障信息需要单独开启才会返回

## 官方文档链接
[https://open.oceanengine.com/labels/12/docs/1771195810853899](https://open.oceanengine.com/labels/12/docs/1771195810853899)