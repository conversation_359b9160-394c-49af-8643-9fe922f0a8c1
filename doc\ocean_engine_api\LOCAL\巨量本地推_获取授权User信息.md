# 巨量本地推 - 获取授权User信息

## 接口描述
API授权是以User为纬度的，Access Token记录了授权User信息；通过此接口可以获取每一个Access Token对应的User信息，方便开发者区分以及管理对应授权关系。

## 请求地址
```
https://api.oceanengine.com/open_api/2/user/info/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Access-Token | string | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/2/user/info/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == "__main__":
    # Args in JSON format
    my_args = "{}" % ()
    print(get(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ display_name | string | 用户名 |
| ├─ email | string | 邮箱（已经脱敏处理） |
| ├─ id | number | 用户id |
| ├─ material_auth_status | bool | 是否敏感物料授权，true 已敏感物料授权，false 未敏感物料授权 |
| ├─ app_id | number | 授权的应用id |
| ├─ token_scope_list | number[] | 权限点list |
| ├─ token_api_list | string[] | 当前token可操作的api接口列表 |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "display_name": "示例用户",
    "email": "example***@example.com",
    "id": 12345678901234567890,
    "material_auth_status": true,
    "app_id": 9876543210,
    "token_scope_list": [1, 2, 3, 4, 5],
    "token_api_list": [
      "/open_api/2/advertiser/info/",
      "/open_api/v3.0/local/promotion/list/",
      "/open_api/v3.0/local/promotion/detail/",
      "/open_api/v3.0/local/project/list/",
      "/open_api/v3.0/local/project/detail/"
    ]
  },
  "request_id": "20240101123456789abcdef"
}
```

## 官方文档链接
[获取授权User信息](https://open.oceanengine.com/labels/37/docs/1696710507039756?origin=left_nav)