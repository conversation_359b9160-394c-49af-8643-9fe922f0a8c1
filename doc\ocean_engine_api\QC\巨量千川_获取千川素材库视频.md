# 获取千川素材库视频

## 接口描述
用于获取千川素材库中的视频素材列表。

## 请求地址
https://api.oceanengine.com/open_api/v1.0/qianchuan/creative/material/video/get/

## 请求方法
GET

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Access-Token | string | 必填，授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| advertiser_id | number | 必填，千川广告账户ID |
| filtering | object | 过滤条件 |
| └─ material_ids | string[] | 按素材ID过滤 |
| └─ video_duration_min | number | 视频最小时长，单位：秒 |
| └─ video_duration_max | number | 视频最大时长，单位：秒 |
| └─ width | number | 视频宽度 |
| └─ height | number | 视频高度 |
| └─ ratio | number[] | 视频宽高比过滤 |
| └─ start_time | string | 开始时间，格式：yyyy-MM-dd |
| └─ end_time | string | 结束时间，格式：yyyy-MM-dd |
| └─ video_type | string[] | 视频类型过滤，允许值：<br>• SINGLE_VIDEO：单视频<br>• MICRO_VIDEO：微视频<br>• CAROUSEL_VIDEO：轮播视频 |
| page | number | 页码，默认值：1 |
| page_size | number | 页面大小，默认值：10，允许值：10, 20, 50, 100 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "YOUR_ACCESS_TOKEN"
PATH = "/open_api/v1.0/qianchuan/creative/material/video/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    return urlunparse(("https", "ad.oceanengine.com", path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == "__main__":
    # Args in JSON format
    my_args = "{\"advertiser_id\": ADVERTISER_ID,\"filtering\": {},\"page\": 1,\"page_size\": 10}"
    print(get(my_args))
```

## 应答字段
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | object | json返回值 |
| └─ list | object[] | 视频素材列表 |
| └─ └─ material_id | string | 素材ID |
| └─ └─ filename | string | 文件名 |
| └─ └─ video_url | string | 视频URL |
| └─ └─ cover_image_url | string | 视频封面URL |
| └─ └─ signature | string | 视频签名 |
| └─ └─ width | number | 视频宽度 |
| └─ └─ height | number | 视频高度 |
| └─ └─ ratio | number | 视频宽高比 |
| └─ └─ duration | number | 视频时长，单位：秒 |
| └─ └─ format | string | 视频格式 |
| └─ └─ size | number | 文件大小，单位：字节 |
| └─ └─ bit_rate | number | 视频码率 |
| └─ └─ frame_rate | number | 视频帧率 |
| └─ └─ video_type | string | 视频类型 |
| └─ └─ create_time | string | 创建时间 |
| └─ └─ modify_time | string | 修改时间 |
| └─ └─ source | string | 素材来源 |
| └─ └─ material_tags | string[] | 素材标签 |
| └─ └─ poster_url | string | 视频海报URL |
| └─ page_info | object | 分页信息 |
| └─ └─ page | number | 页码 |
| └─ └─ page_size | number | 页面大小 |
| └─ └─ total_number | number | 总数 |
| └─ └─ total_page | number | 总页数 |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "request_id": "202105111551080102121931483A043E53",
  "data": {
    "list": [
      {
        "material_id": "video123456789",
        "filename": "example_video.mp4",
        "video_url": "https://example.com/video.mp4",
        "cover_image_url": "https://example.com/cover.jpg",
        "signature": "abc123def456",
        "width": 1920,
        "height": 1080,
        "ratio": 1.78,
        "duration": 30,
        "format": "mp4",
        "size": 5247680,
        "bit_rate": 1400,
        "frame_rate": 25,
        "video_type": "SINGLE_VIDEO",
        "create_time": "2021-05-11 15:49:01",
        "modify_time": "2021-05-11 15:49:01",
        "source": "UPLOADED",
        "material_tags": ["产品介绍", "宣传"],
        "poster_url": "https://example.com/poster.jpg"
      }
    ],
    "page_info": {
      "page": 1,
      "page_size": 10,
      "total_number": 15,
      "total_page": 2
    }
  }
}
```

## 官方文档链接
https://open.oceanengine.com/labels/12/docs/1778159308092424?origin=left_nav