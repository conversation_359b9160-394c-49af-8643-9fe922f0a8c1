# 巨量广告 - 获取广告列表

## 接口描述
通过此接口，用户可以获取广告主下的广告信息，支持多种过滤条件。

## 请求地址
```
GET https://ad.oceanengine.com/open_api/2/ad/get/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主ID |
| filtering | object | 可选 | 过滤条件 |
| └─ ids | number[] | 可选 | 按广告计划ID过滤，数量限制：<=100 |
| └─ ad_name | string | 可选 | 按广告计划名称过滤，支持模糊匹配 |
| └─ pricing_list | string[] | 可选 | 按出价方式过滤，允许值：<br/>• `PRICING_CPC` 点击出价<br/>• `PRICING_CPM` 展示出价<br/>• `PRICING_CPA` 转化出价<br/>• `PRICING_CPV` 播放出价<br/>• `PRICING_OCPM` 优化展示出价<br/>• `PRICING_OCPC` 优化点击出价 |
| └─ status | string[] | 可选 | 按计划状态过滤，允许值：<br/>• `AD_STATUS_ENABLE` 启用<br/>• `AD_STATUS_DISABLE` 暂停<br/>• `AD_STATUS_DELETE` 删除<br/>• `AD_STATUS_AUDIT` 审核中<br/>• `AD_STATUS_DONE` 投放完成 |
| └─ ad_create_time | object | 可选 | 按创建时间过滤 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ start | string | 可选 | 起始时间，格式：yyyy-MM-dd |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ end | string | 可选 | 结束时间，格式：yyyy-MM-dd |
| └─ ad_modify_time | object | 可选 | 按更新时间过滤 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ start | string | 可选 | 起始时间，格式：yyyy-MM-dd |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ end | string | 可选 | 结束时间，格式：yyyy-MM-dd |
| └─ campaign_ids | number[] | 可选 | 按广告组ID过滤，数量限制：<=100 |
| └─ ad_tags | string[] | 可选 | 按广告标签过滤 |
| └─ pricing | string[] | 可选 | 按出价方式过滤，允许值：<br/>• `PRICING_CPC` 点击出价<br/>• `PRICING_CPM` 展示出价<br/>• `PRICING_CPA` 转化出价<br/>• `PRICING_CPV` 播放出价<br/>• `PRICING_OCPM` 优化展示出价<br/>• `PRICING_OCPC` 优化点击出价 |
| └─ first_industry_id | number | 可选 | 一级行业ID |
| └─ second_industry_id | number | 可选 | 二级行业ID |
| └─ third_industry_id | number | 可选 | 三级行业ID |
| └─ delivery_mode | string[] | 可选 | 投放模式过滤，允许值：<br/>• `MANUAL` 手动投放<br/>• `AUTO` 自动投放 |
| └─ flow_control_mode | string[] | 可选 | 流量控制模式过滤 |
| └─ union_video_type | string[] | 可选 | 穿山甲视频类型过滤 |
| └─ inventory_type | string[] | 可选 | 广告库存类型过滤 |
| └─ bid_strategy | string[] | 可选 | 出价策略过滤 |
| └─ smart_bid_type | string[] | 可选 | 智能出价类型过滤 |
| └─ budget_optimize_switch | string[] | 可选 | 预算优化开关过滤 |
| └─ budget_optimize_goal | string[] | 可选 | 预算优化目标过滤 |
| └─ creative_material_mode | string[] | 可选 | 创意素材模式过滤 |
| └─ audience_package | string[] | 可选 | 定向包过滤 |
| └─ deep_external_action | string[] | 可选 | 深度转化行为过滤 |
| └─ external_action | string[] | 可选 | 转化行为过滤 |
| └─ conversion_id | number[] | 可选 | 转化ID过滤 |
| └─ deep_conversion_id | number[] | 可选 | 深度转化ID过滤 |
| └─ operation_status | string[] | 可选 | 操作状态过滤 |
| └─ delivery_version | string[] | 可选 | 投放版本过滤 |
| └─ promotion_type | string[] | 可选 | 推广类型过滤 |
| └─ learning_phase | string[] | 可选 | 学习期状态过滤，允许值：<br/>• `LEARNING` 学习期<br/>• `LEARNED` 已学习<br/>• `LEARNING_LIMITED` 受限学习期 |
| └─ opt_status | string[] | 可选 | 优化状态过滤，允许值：<br/>• `AD_STATUS_PAUSED_BY_FUND` 账户余额不足<br/>• `AD_STATUS_PAUSED_BY_HOURLY_BUDGET` 小时预算不足<br/>• `AD_STATUS_PAUSED_BY_BUDGET` 预算不足<br/>• `AD_STATUS_PAUSED_BY_ADVERTISER_BALANCE` 广告主账户余额不足<br/>• `AD_STATUS_PAUSED` 已暂停<br/>• `AD_STATUS_ACTIVE` 投放中<br/>• `AD_STATUS_NOT_START` 未开始<br/>• `AD_STATUS_DONE` 已完成<br/>• `AD_STATUS_AUDIT` 审核中<br/>• `AD_STATUS_AUDIT_DENY` 审核拒绝 |
| └─ is_feed_and_fav_see | string | 可选 | 是否推荐偏向设置，允许值：ON、OFF |
| └─ hide_if_converted | string | 可选 | 转化后是否隐藏广告，允许值：<br/>• `ON` 开启<br/>• `OFF` 关闭 |
| └─ hide_if_exists | string | 可选 | 存在时是否隐藏，允许值：<br/>• `ON` 开启<br/>• `OFF` 关闭 |
| └─ ad_delivery_switch | string | 可选 | 广告投放开关过滤，允许值：<br/>• `ON` 开启<br/>• `OFF` 关闭 |
| └─ values_setting | object | 可选 | 值设置过滤 |
| └─ province_id | string | 可选 | 省份ID过滤，参考【枚举值-地域】说明 |
| └─ anchor_related_type | string | 可选 | 主播关联类型过滤 |
| └─ anchor_type | string | 可选 | 主播类型过滤，允许值：<br/>• `SELF_SUPPORT_ANCHOR` 自营主播<br/>• `THIRD_PARTY_ANCHOR` 第三方主播<br/>• `OWN_LIVE` 自有直播<br/>• `AWEME_LIVE` 抖音直播 |
| └─ open_url | string | 可选 | 直达链接 |
| └─ sort_url | string | 可选 | 排序链接 |
| └─ schedule_type | string | 可选 | 投放时间类型，允许值：<br/>• `SCHEDULE_FROM_NOW` 从现在开始一直投放<br/>• `SCHEDULE_START_END` 设置开始和结束时间 |
| └─ audience_generation | string | 可选 | 定向类型 |
| └─ promotion_materials | object | 可选 | 推广素材 |
| └─ promo_type | string | 可选 | 推广类型 |
| └─ external_url_field | string | 可选 | 外部链接字段 |
| └─ external_url_params | string | 可选 | 外部链接参数 |
| └─ external_url_material_list | string[] | 可选 | 外部链接素材列表 |
| └─ convert_material_list | object | 可选 | 转化素材列表 |
| └─ anchor_material_list | object | 可选 | 主播素材列表 |
| └─ open_url | string | 可选 | 直达链接 |
| └─ sort_url | string | 可选 | 排序链接 |
| └─ schedule_fixed_type | string | 可选 | 投放时间固定类型 |
| └─ audience_generation | string | 可选 | 定向类型 |
| └─ promotion_materials | object | 可选 | 推广素材 |
| └─ promo_type | string | 可选 | 推广类型 |
| └─ external_url_field | string | 可选 | 外部链接字段 |
| └─ external_url_params | string | 可选 | 外部链接参数 |
| └─ external_url_material_list | string[] | 可选 | 外部链接素材列表 |
| └─ component_material_list | object | 可选 | 组件素材列表 |
| └─ component_id | number | 可选 | 组件ID |
| └─ anchor_material_list | object | 可选 | 主播素材列表 |
| └─ anchor_type | string | 可选 | 主播类型 |
| └─ open_url | string | 可选 | 直达链接 |
| └─ sort_url | string | 可选 | 排序链接 |
| └─ schedule_fixed_type | string | 可选 | 投放时间固定类型 |
| └─ audience_generation | string | 可选 | 定向类型 |
| └─ promotion_materials | object | 可选 | 推广素材 |
| └─ promo_type | string | 可选 | 推广类型 |
| └─ external_url_field | string | 可选 | 外部链接字段 |
| └─ external_url_params | string | 可选 | 外部链接参数 |
| └─ external_url_material_list | string[] | 可选 | 外部链接素材列表 |
| └─ open_url_type | string | 可选 | 开放链接类型 |
| └─ open_url_field | string | 可选 | 开放链接字段 |
| └─ external_url_params | string | 可选 | 外部链接参数 |
| └─ smart_interest_action_switch | string | 可选 | 智能兴趣行为开关 |
| └─ ad_keywords | string[] | 可选 | 广告关键词过滤 |
| └─ keywords | string[] | 可选 | 关键词过滤 |
| └─ word | string | 可选 | 关键词 |
| └─ match_type | string | 可选 | 匹配类型，允许值：<br/>• `PHRASE` 短语匹配<br/>• `EXTENSIVE` 广泛匹配<br/>• `PRECISION` 精确匹配 |
| └─ bid | float | 可选 | 出价 |
| └─ is_commentable | bool | 可选 | 是否可评论 |
| └─ neg_word_info | object | 可选 | 否定关键词信息 |
| └─ component_material_list | object | 可选 | 组件素材列表 |
| └─ component_id | number | 可选 | 组件ID |
| └─ anchor_material_list | object | 可选 | 主播素材列表 |
| └─ anchor_type | string | 可选 | 主播类型 |
| └─ external_url_material_list | string[] | 可选 | 外部链接素材列表 |
| └─ oss_program_info | object | 可选 | 学习与提升信息 |
| └─ app_id | string | 可选 | 小程序ID |
| └─ start_path | string | 可选 | 小程序启动路径 |
| └─ params | string | 可选 | 启动参数 |
| └─ url | string | 可选 | 学习与提升链接 |
| └─ urls | string[] | 可选 | 自定义落地页链接（多个） |
| └─ web | string | 可选 | 自定义落地页链接 |
| └─ app_id | string | 可选 | 小程序ID |
| └─ start_path | string | 可选 | 小程序启动路径 |
| └─ params | string | 可选 | 启动参数 |
| └─ web_url_material_list | string[] | 可选 | 网页链接素材列表 |
| └─ playable_url_material_list | string[] | 可选 | 可玩广告素材列表 |
| └─ ad_is_action_buttons | string[] | 可选 | 行动按钮过滤 |
| └─ component_generation | string | 可选 | 组件生成 |
| └─ intelligent_generation | string | 可选 | 智能生成 |
| └─ promotion_materials | object | 可选 | 推广素材 |
| └─ promo_type | string | 可选 | 推广类型 |
| └─ external_url_field | string | 可选 | 外部链接字段 |
| └─ external_url_params | string | 可选 | 外部链接参数 |
| └─ anchor_material_list | object | 可选 | 主播素材列表 |
| └─ image_subject | object | 可选 | 图片主体 |
| └─ red | string | 可选 | 红色 |
| └─ description | string | 可选 | 描述 |
| └─ brand | string | 可选 | 品牌 |
| └─ comment | string | 可选 | 评论 |
| └─ cta_plus_material_list | object | 可选 | CTA+素材列表 |
| └─ image_mode | string | 可选 | 素材类型，允许值：<br/>• `IMAGE_MODE_SINGLE_LR` 单图（横版）<br/>• `IMAGE_MODE_SINGLE_VERTICAL` 单图（竖版） |
| └─ app_file_url | string | 可选 | 应用文件URL |
| └─ app_file_version | string | 可选 | 应用文件版本 |
| └─ file_name | string | 可选 | 文件名 |
| └─ material_id | number | 可选 | 素材ID |
| └─ goods_views_id | string | 可选 | 商品浏览ID |
| └─ material_opt_status | string | 可选 | 素材优化状态，允许值：<br/>• `SYSTEM` 系统<br/>• `ENABLE` 启用 |
| └─ material_status | string | 可选 | 素材状态，允许值：<br/>• `MATERIAL_STATUS_NOT_DELETE` 未删除<br/>• `MATERIAL_STATUS_DELETE` 已删除<br/>• `MATERIAL_STATUS_AUDIT` 审核中<br/>• `MATERIAL_STATUS_PASS` 审核通过<br/>• `MATERIAL_STATUS_REJECT` 审核拒绝<br/>• `MATERIAL_STATUS_FROZEN` 已冻结 |
| └─ dynamic_creative_switch | string | 可选 | 动态创意开关，允许值：<br/>• `ON` 开启<br/>• `OFF` 关闭 |
| └─ advanced_dc_settings | string[] | 可选 | 高级动态创意设置，允许值：<br/>• `DYNAMIC_AUDIENCE_OCPC` 动态受众OCPC<br/>• `DYNAMIC_AUDIENCE_OCPC_ENHANCED` 动态受众OCPC增强 |
| └─ auto_extend_traffic | string | 可选 | 自动扩展流量，允许值：<br/>• `ON` 开启<br/>• `OFF` 关闭 |
| └─ keywords | string[] | 可选 | 关键词过滤 |
| └─ word | string | 可选 | 关键词 |
| └─ match_type | string | 可选 | 匹配类型，允许值：<br/>• `PHRASE` 短语匹配<br/>• `EXTENSIVE` 广泛匹配<br/>• `PRECISION` 精确匹配 |
| └─ bid | float | 可选 | 出价 |
| └─ component_material_list | object | 可选 | 组件素材列表 |
| └─ component_id | number | 可选 | 组件ID |
| └─ anchor_material_list | object | 可选 | 主播素材列表 |
| └─ external_url_material_list | string[] | 可选 | 外部链接素材列表 |
| └─ mix_program_info | object | 可选 | 学习与提升信息 |
| └─ app_id | string | 可选 | 小程序ID |
| └─ start_path | string | 可选 | 小程序启动路径 |
| └─ params | string | 可选 | 启动参数 |
| └─ url | string | 可选 | 学习与提升链接 |
| └─ web_url_material_list | string[] | 可选 | 网页链接素材列表 |
| └─ playable_url_material_list | string[] | 可选 | 可玩广告素材列表 |
| └─ ad_is_action_buttons | string[] | 可选 | 行动按钮过滤 |
| └─ component_generation | string | 可选 | 组件生成 |
| └─ intelligent_generation | string | 可选 | 智能生成 |
| └─ library | string | 可选 | 文件库 |
| └─ is_comment_disable | string | 可选 | 是否禁用评论，允许值：<br/>• `ON` 禁用<br/>• `OFF` 不禁用 |
| └─ ad_download_status | string | 可选 | 广告下载状态，允许值：<br/>• `ON` 允许下载<br/>• `OFF` 不允许下载 |
| └─ materials_type | string | 可选 | 素材类型 |
| └─ budget | number | 可选 | 预算过滤 |
| └─ budget_mode | string | 可选 | 预算模式过滤，允许值：<br/>• `BUDGET_MODE_DAY` 日预算<br/>• `BUDGET_MODE_TOTAL` 总预算 |
| └─ bid | number | 可选 | 出价过滤 |
| └─ cpa_bid | number | 可选 | CPA出价过滤 |
| └─ deep_cpabid | number | 可选 | 深度CPA出价过滤 |
| └─ roi_goal | number | 可选 | ROI目标过滤 |
| fields | string[] | 可选 | 查询字段集合，默认查询所有。详见下方字段说明 |
| page | number | 可选 | 页码，默认值：1 |
| page_size | number | 可选 | 页面大小，默认值：10，取值范围：1-500 |

### 可查询字段（fields）说明
- `id` - 广告计划ID
- `name` - 广告计划名称
- `budget` - 预算金额
- `budget_mode` - 预算类型
- `status` - 计划状态
- `opt_status` - 优化状态
- `delivery_range` - 投放范围
- `smart_bid_type` - 智能出价类型
- `pricing` - 出价方式
- `cpa_bid` - CPA出价
- `deep_cpabid` - 深度CPA出价
- `roi_goal` - ROI目标
- `deep_bid_type` - 深度出价类型
- `bid` - 出价
- `schedule_type` - 投放时间类型
- `schedule_time` - 投放时间段
- `union_video_type` - 穿山甲视频类型
- `inventory_type` - 广告库存类型
- `feed_delivery_search` - 信息流投放搜索
- `search_bid_ratio` - 搜索出价比例
- `bid_strategy` - 出价策略
- `budget_optimize_switch` - 预算优化开关
- `budget_optimize_goal` - 预算优化目标
- `creative_material_mode` - 创意素材模式
- `audience_package` - 定向包
- `deep_external_action` - 深度转化行为
- `external_action` - 转化行为
- `conversion_id` - 转化ID
- `deep_conversion_id` - 深度转化ID
- `campaign_id` - 关联项目ID
- `operation_status` - 操作状态
- `delivery_version` - 投放版本
- `promotion_type` - 推广类型
- `create_time` - 创建时间
- `modify_time` - 修改时间
- `learning_phase` - 学习期状态
- `flow_control_mode` - 流量控制模式
- `budget_optimize_bid` - 预算优化出价
- `deep_bid_type` - 深度出价类型
- `audience_extend` - 受众扩展
- `retargeting_tags_include` - 包含的重定向标签
- `retargeting_tags_exclude` - 排除的重定向标签
- `interest_action_mode` - 兴趣行为模式
- `action_categories` - 行为类别
- `action_words` - 行为关键词
- `action_days` - 行为天数
- `interest_categories` - 兴趣类别
- `interest_words` - 兴趣关键词
- `age` - 年龄范围
- `gender` - 性别
- `district` - 地域
- `business_ids` - 商圈ID
- `exclude_business_ids` - 排除商圈ID
- `ac` - 行为兴趣分类
- `ac_tags` - 行为兴趣标签
- `carrier` - 运营商
- `platform` - 操作系统
- `network_type` - 网络类型
- `device_brand` - 设备品牌
- `device_type` - 设备类型
- `os_version` - 操作系统版本
- `delivery_range` - 投放范围
- `price` - 价格
- `launch_price` - 起投价格
- `union_bid_type` - 穿山甲出价类型
- `hide_if_converted` - 转化后隐藏
- `hide_if_exists` - 存在时隐藏
- `convert_optimized_type` - 转化优化类型
- `convert_id` - 转化ID
- `deep_convert_id` - 深度转化ID
- `convert_optimized_goal` - 转化优化目标
- `deep_convert_optimized_goal` - 深度转化优化目标
- `roi_goal` - ROI目标
- `pay_mode` - 支付模式
- `deep_pay_mode` - 深度支付模式
- `budget_optimize_cpa_bid` - 预算优化CPA出价
- `budget_optimize_deep_bid` - 预算优化深度出价
- `enable_inventory_filter` - 启用库存过滤
- `inventory_type_list` - 库存类型列表
- `feed_delivery_search` - 信息流投放搜索
- `search_bid_ratio` - 搜索出价比例
- `flow_package` - 流量包
- `ad_keywords` - 广告关键词

## 请求示例

```python
import requests
import json

def get_ad_list():
    open_api_url_prefix = "https://ad.oceanengine.com/open_api/"
    uri = "2/ad/get/"
    url = open_api_url_prefix + uri
    
    params = {
        "advertiser_id": 123456789,
        "filtering": {
            "ad_name": "测试广告",
            "status": ["AD_STATUS_ENABLE", "AD_STATUS_DISABLE"],
            "pricing_list": ["PRICING_CPC"],
            "ad_create_time": {
                "start": "2023-01-01",
                "end": "2023-12-31"
            }
        },
        "fields": ["id", "name", "budget", "status", "pricing"],
        "page": 1,
        "page_size": 50
    }
    
    headers = {"Access-Token": "your_access_token"}
    
    # Convert filtering to JSON string for URL encoding
    if 'filtering' in params:
        params['filtering'] = json.dumps(params['filtering'])
    if 'fields' in params:
        params['fields'] = json.dumps(params['fields'])
    
    rsp = requests.get(url, params=params, headers=headers)
    rsp_data = rsp.json()
    return rsp_data
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ list | object[] | 广告计划列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ id | number | 广告计划ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ name | string | 广告计划名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget | number | 预算金额，单位：分 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget_mode | string | 预算类型<br/>• `BUDGET_MODE_DAY` 日预算<br/>• `BUDGET_MODE_TOTAL` 总预算 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ status | string | 计划状态 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ opt_status | string | 优化状态 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ delivery_range | string | 投放范围 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ smart_bid_type | string | 智能出价类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ pricing | string | 出价方式 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ cpa_bid | number | CPA出价，单位：分 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ deep_cpabid | number | 深度CPA出价，单位：分 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ roi_goal | number | ROI目标 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ deep_bid_type | string | 深度出价类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ bid | number | 出价，单位：分 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ schedule_type | string | 投放时间类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ schedule_time | string | 投放时间段 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ union_video_type | string | 穿山甲视频类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ inventory_type | array | 广告库存类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ feed_delivery_search | string | 信息流投放搜索 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ search_bid_ratio | number | 搜索出价比例 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ bid_strategy | string | 出价策略 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget_optimize_switch | string | 预算优化开关 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget_optimize_goal | string | 预算优化目标 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ creative_material_mode | string | 创意素材模式 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ audience_package | object | 定向包信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ deep_external_action | string | 深度转化行为 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ external_action | string | 转化行为 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ conversion_id | number | 转化ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ deep_conversion_id | number | 深度转化ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ campaign_id | number | 关联项目ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ operation_status | string | 操作状态 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ delivery_version | string | 投放版本 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_type | string | 推广类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ create_time | string | 创建时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ modify_time | string | 修改时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ learning_phase | string | 学习期状态 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ flow_control_mode | string | 流量控制模式 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget_optimize_bid | number | 预算优化出价 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ audience_extend | object | 受众扩展 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ retargeting_tags_include | array | 包含的重定向标签 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ retargeting_tags_exclude | array | 排除的重定向标签 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ interest_action_mode | string | 兴趣行为模式 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ action_categories | array | 行为类别 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ action_words | array | 行为关键词 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ action_days | number | 行为天数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ interest_categories | array | 兴趣类别 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ interest_words | array | 兴趣关键词 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ age | array | 年龄范围 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ gender | string | 性别 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ district | string | 地域 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ business_ids | array | 商圈ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ exclude_business_ids | array | 排除商圈ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ ac | array | 行为兴趣分类 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ ac_tags | array | 行为兴趣标签 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ carrier | array | 运营商 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ platform | array | 操作系统 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ network_type | array | 网络类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ device_brand | array | 设备品牌 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ device_type | array | 设备类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ os_version | array | 操作系统版本 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ price | number | 价格 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ launch_price | number | 起投价格 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ union_bid_type | string | 穿山甲出价类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ hide_if_converted | string | 转化后隐藏 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ hide_if_exists | string | 存在时隐藏 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ convert_optimized_type | string | 转化优化类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ convert_id | number | 转化ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ deep_convert_id | number | 深度转化ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ convert_optimized_goal | string | 转化优化目标 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ deep_convert_optimized_goal | string | 深度转化优化目标 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ pay_mode | string | 支付模式 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ deep_pay_mode | string | 深度支付模式 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget_optimize_cpa_bid | number | 预算优化CPA出价 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget_optimize_deep_bid | number | 预算优化深度出价 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ enable_inventory_filter | boolean | 启用库存过滤 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ inventory_type_list | array | 库存类型列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ flow_package | string | 流量包 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ ad_keywords | array | 广告关键词 |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 当前页 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ page_size | number | 页面大小 |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "list": [
      {
        "id": 1234567890,
        "name": "测试广告计划",
        "budget": 100000,
        "budget_mode": "BUDGET_MODE_DAY",
        "status": "AD_STATUS_ENABLE",
        "opt_status": "AD_OPT_STATUS_ACTIVE",
        "delivery_range": "DEFAULT",
        "smart_bid_type": "SMART_BID_CONSERVATIVE",
        "pricing": "PRICING_CPC",
        "cpa_bid": 50000,
        "deep_cpabid": 60000,
        "roi_goal": 200,
        "deep_bid_type": "DEEP_BID_MIN",
        "bid": 300,
        "schedule_type": "SCHEDULE_FROM_NOW",
        "schedule_time": "0100111010001110100011101000111",
        "union_video_type": "ORIGINAL_VIDEO",
        "inventory_type": ["UNION_SLOT_VIDEO"],
        "feed_delivery_search": "SEARCH_DISABLE",
        "search_bid_ratio": 100,
        "bid_strategy": "BID_STRATEGY_CPC",
        "budget_optimize_switch": "OFF",
        "budget_optimize_goal": "GOAL_CPM",
        "creative_material_mode": "INTERVENE",
        "audience_package": {
          "id": 123456,
          "name": "自定义定向包"
        },
        "deep_external_action": "DEEP_EXTERNAL_ACTION_CONSULT",
        "external_action": "EXTERNAL_ACTION_DOWNLOAD",
        "conversion_id": 789012,
        "deep_conversion_id": 789013,
        "campaign_id": 987654321,
        "operation_status": "ENABLE",
        "delivery_version": "v2.0",
        "promotion_type": "APP",
        "create_time": "2023-01-15 10:30:00",
        "modify_time": "2023-01-16 14:20:00",
        "learning_phase": "LEARNING_PHASE_LEARNED",
        "flow_control_mode": "FLOW_CONTROL_MODE_FAST",
        "budget_optimize_bid": 400,
        "audience_extend": {
          "enabled": true,
          "extend_targets": ["LOOKALIKE"]
        },
        "retargeting_tags_include": ["tag1", "tag2"],
        "retargeting_tags_exclude": ["tag3"],
        "interest_action_mode": "UNLIMITED",
        "action_categories": [23, 24],
        "action_words": ["游戏", "娱乐"],
        "action_days": 30,
        "interest_categories": [1001, 1002],
        "interest_words": ["科技", "数码"],
        "age": ["AGE_18_24", "AGE_25_34"],
        "gender": "GENDER_UNLIMITED",
        "district": "DISTRICT_LEVEL_CITY",
        "business_ids": [1001, 1002],
        "exclude_business_ids": [2001],
        "ac": ["AC1", "AC2"],
        "ac_tags": ["tag_ac1", "tag_ac2"],
        "carrier": ["MOBILE", "UNICOM"],
        "platform": ["ANDROID", "IOS"],
        "network_type": ["WIFI", "4G"],
        "device_brand": ["HUAWEI", "APPLE"],
        "device_type": ["PHONE"],
        "os_version": ["13.0", "14.0"],
        "price": 5000,
        "launch_price": 3000,
        "union_bid_type": "UNION_BID_CPC",
        "hide_if_converted": "HIDE_IF_CONVERTED_ENABLE",
        "hide_if_exists": "HIDE_IF_EXISTS_DISABLE",
        "convert_optimized_type": "CVR",
        "convert_id": 789014,
        "deep_convert_id": 789015,
        "convert_optimized_goal": "CONVERT_OPTIMIZED_GOAL_DOWNLOAD",
        "deep_convert_optimized_goal": "DEEP_CONVERT_OPTIMIZED_GOAL_REGISTER",
        "pay_mode": "PAY_MODE_CPC",
        "deep_pay_mode": "DEEP_PAY_MODE_CPA",
        "budget_optimize_cpa_bid": 500,
        "budget_optimize_deep_bid": 600,
        "enable_inventory_filter": true,
        "inventory_type_list": ["INVENTORY_FEED", "INVENTORY_VIDEO_FEED"],
        "flow_package": "FLOW_PACKAGE_PREMIUM",
        "ad_keywords": ["关键词1", "关键词2"]
      }
    ],
    "page_info": {
      "total_number": 100,
      "total_page": 10,
      "page": 1,
      "page_size": 10
    }
  },
  "request_id": "2023011610300012345"
}
```

## 说明
- 本API支持获取广告主下的广告计划详细信息
- 支持多种过滤条件，包括状态、出价方式、创建时间等
- 返回字段非常丰富，包含投放配置、定向设置、出价策略等完整信息
- 推荐使用fields参数指定需要的字段，避免返回过多不必要的数据

## 官方文档链接
[获取广告列表-巨量广告升级版](https://open.oceanengine.com/labels/7/docs/1741028841006095?origin=left_nav)