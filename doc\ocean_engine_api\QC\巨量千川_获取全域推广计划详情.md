# 巨量千川 - 获取全域推广计划详情

## 接口概述
获取全域推广计划详情

## 请求信息

### 请求地址
```
GET https://api.oceanengine.com/open_api/v1.0/qianchuan/uni_promotion/detail/
```

### 请求头
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Access-Token | string | 是 | 授权access_token，可以通过【获取Access token】接口获取 |

### 请求参数
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| advertiser_id | number | 是 | 广告主ID |
| ad_id | number | 是 | 全域推广计划ID |
| start_time | string | 是 | 开始时间，格式：2021-04-05 00:00:00 |
| end_time | string | 是 | 结束时间，格式：2021-04-06 00:00:00 |
| fields | string[] | 是 | 需要查询的消耗指标 |

### 请求示例
```python
# coding=utf-8
import json
import requests
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/uni_promotion/detail/"

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, str) else json.dumps(v) for k, v in args.items()})
    url = f"https://api.oceanengine.com{PATH}?{query_string}"
    headers = {"Access-Token": ACCESS_TOKEN}
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    my_args = json.dumps({
        "advertiser_id": 123456789,
        "ad_id": 987654321,
        "start_time": "2024-01-01 00:00:00",
        "end_time": "2024-01-31 23:59:59",
        "fields": ["stat_cost", "total_prepay_and_pay_order_roi2"]
    })
    print(get(my_args))
```

## 响应信息

### 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码 |
| message | string | 返回信息 |
| data | object | 返回数据 |
| ad_info | object | 计划详情信息 |
| stats_info | object | 消耗指标信息 |
| request_id | string | 请求日志id |

### 响应示例
```json
{
    "code": 0,
    "message": "OK",
    "data": {
        "ad_info": {
            "id": 987654321,
            "name": "全域推广计划详情",
            "status": "DELIVERY_OK",
            "marketing_goal": "LIVE_PROM_GOODS"
        },
        "stats_info": {
            "stat_cost": 5000.0,
            "total_prepay_and_pay_order_roi2": 4.2
        },
        "request_id": "20240101123456789"
    }
}
```

## 官方文档链接
[https://open.oceanengine.com/labels/12/docs/1804362305657868](https://open.oceanengine.com/labels/12/docs/1804362305657868)