# 巨量千川 - 获取账户下素材列表

## 接口概述
获取账户下素材列表和数据

## 请求信息

### 请求地址
```
GET https://api.oceanengine.com/open_api/v1.0/qianchuan/material/get/
```

### 请求头
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Access-Token | string | 是 | 授权access_token，可以通过【获取Access token】接口获取 |

### 请求参数
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| advertiser_id | number | 是 | 千川广告账户ID |
| marketing_goal | string | 是 | 营销目标：<br>- `LIVE_PROM_GOODS`：推直播间<br>- `VIDEO_PROM_GOODS`：推商品 |
| campaign_scene | string[] | 否 | 按营销场景过滤，不传默认查询全部。允许值：<br>**直播场景**：<br>- `DAILY_SALE`：日常销售<br>- `LIVE_HEAT`：直播间加热<br>**推商品场景**：<br>- `DAILY_SALE`：日常销售<br>- `PLANT_GRASS`：人群种草 |
| marketing_scene | string | 是 | 广告类型过滤：<br>- `FEED`：通投<br>- `SEARCH`：搜索<br>- `SHOPPING_MALL`：商城广告 |
| filtering | object | 是 | 过滤器 |
| material_type | string | 是 | 素材类型：<br>- `IMAGE`：图片，图文<br>- `LIVE_ROOM`：直播间画面<br>- `TITLE`：标题<br>- `VIDEO`：视频<br>**注意**：直播间画面仅支持推直播间计划，图片仅支持推商品计划 |
| image_mode | string[] | 否 | 素材样式，仅material_type=VIDEO/IMAGE时支持<br>**VIDEO时支持**：<br>- `VIDEO_LARGE`：横版视频<br>- `VIDEO_VERTICAL`：竖版视频<br>**IMAGE时支持**：<br>- `SMALL`：横版小图<br>- `LARGE`：横版大图<br>- `LARGE_VERTICAL`：竖版图片<br>- `CAROUSEL`：图文 |
| having_cost | string | 否 | 消耗情况，仅material_type=VIDEO/IMAGE时支持：<br>- `ALL`：全部<br>- `"YES"`：有消耗<br>默认查询全部 |
| video_source | string[] | 否 | 视频来源，仅material_type=VIDEO时支持：<br>- `AWEME`：抖音主页视频<br>- `E_COMMERCE`：本地上传<br>- `LIVE_HIGHLIGHT`：直播剪辑素材<br>- `BP`：巨量纵横共享素材<br>- `VIDEO_CAPTURE`：易拍APP共享素材<br>- `ARTHUR`：亚瑟共享素材<br>- `STAR`：星图&即合共享素材<br>- `TADA`：tada共享素材<br>- `CREATIVE_CENTER`：巨量创意PC共享素材<br>- `JIANYING`：剪映共享素材<br>- `JI_CHUANG`：即创共享素材<br>- `QUNFENG`：群峰共享素材 |
| analysis_type | string[] | 否 | 素材建议，仅material_type=VIDEO时支持：<br>- `CARRY_MATERIAL`：搬运风险素材<br>- `LOW_EFFICIENCY_MATERIAL`：低效素材<br>- `FIRST_PUBLISH_MATERIAL`：首发素材<br>- `SIMILAR_RISK_MATERIAL`：同质化素材<br>- `HIGH_QUALITY_MATERIAL`：优质素材<br>- `POOR_QUALITY_MATERIAL`：低质素材 |
| search_keyword | string | 否 | 搜索关键词，支持查询直播间/视频/标题/图片名称、直播间/视频/图片id |
| start_time | string | 否 | 数据查询开始时间，精确到秒，格式：yyyy-MM-dd HH:mm:ss |
| end_time | string | 否 | 数据查询结束时间，精确到秒，格式：yyyy-MM-dd HH:mm:ss |
| fields | string[] | 是 | 需要查询的消耗指标，注意：不同素材类型支持的指标有所差异，具体见返回metrics指标 |
| page | number | 否 | 页码，默认值：1 |
| page_size | number | 否 | 页面大小，允许值：10, 20, 50, 100，默认值：10 |
| order_type | string | 否 | 排序方式：<br>- `ASC`：升序<br>- `DESC`：降序（默认） |
| order_field | string | 否 | 排序字段，仅支持根据metrics中字段进行排序，默认stat_cost |

### 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/material/get/"

# 请求参数
advertiser_id = "ADVERTISER_ID"
marketing_goal = "LIVE_PROM_GOODS"
campaign_scene = ["DAILY_SALE"]
marketing_scene = "FEED"
filtering = {
    "material_type": "VIDEO",
    "image_mode": ["VIDEO_LARGE"],
    "having_cost": "ALL",
    "video_source": ["AWEME"],
    "analysis_type": ["HIGH_QUALITY_MATERIAL"],
    "search_keyword": "",
    "start_time": "2024-01-01 00:00:00",
    "end_time": "2024-01-31 23:59:59"
}
fields = ["stat_cost", "show_cnt", "click_cnt", "ctr"]
page = 1
page_size = 10
order_type = "DESC"
order_field = "stat_cost"

# 构建请求URL
query_params = {
    "advertiser_id": advertiser_id,
    "marketing_goal": marketing_goal,
    "campaign_scene": json.dumps(campaign_scene),
    "marketing_scene": marketing_scene,
    "filtering": json.dumps(filtering),
    "fields": json.dumps(fields),
    "page": page,
    "page_size": page_size,
    "order_type": order_type,
    "order_field": order_field
}

query_string = urlencode(query_params)
url = f"https://api.oceanengine.com{PATH}?{query_string}"

headers = {
    "Access-Token": ACCESS_TOKEN
}

response = requests.get(url, headers=headers)
print(response.json())
```

## 响应信息

### 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | json返回值 |
| ad_material_infos | object[] | 返回的素材信息列表 |
| material_info | object | 素材信息 |
| material_type | string | 素材类型 |
| video_material | object | 视频素材 |
| video_id | string | 视频vid |
| image_mode | string | 视频样式 |
| cover_image | object | 视频封面图片 |
| height | number | 高 |
| width | number | 宽 |
| web_url | string | 图片链接 |
| id | string | 图片id |
| material_id | number | 素材id |
| video_duration | number | 视频时长 |
| title | string | 视频标题 |
| source | string | 来源信息 |
| image_material | object | 图片素材 |
| images | object[] | 图片 |
| image_url | string | 图片链接 |
| title_material | object | 标题素材 |
| room_material | object | 直播间画面用户信息 |
| aweme_id | number | 抖音号id |
| aweme_name | string | 抖音号名称 |
| aweme_avatar | string | 抖音号头像链接 |
| metrics | object | 指标信息 |
| material_suggest_reasons | dict[string[]] | 各种标签对应的原因 |
| page_info | object | 分页信息 |
| page | number | 页码 |
| page_size | number | 页面大小 |
| total_page | number | 总页数 |
| total_number | number | 总数 |
| request_id | string | 请求日志id |

### 指标字段说明
| 字段 | 类型 | 描述 |
|------|------|------|
| stat_cost | double | 消耗 |
| show_cnt | double | 展示次数 |
| click_cnt | double | 点击次数 |
| ctr | double | 点击率 |
| cpm_platform | double | 平均千次展现费用 |
| cpc_platform | double | 平均点击单价 |
| total_play | double | 播放数 |
| play_duration_3s | double | 3秒播放数 |
| play_25_feed_break | double | 25%进度播放数 |
| play_50_feed_break | double | 50%进度播放数 |
| play_75_feed_break | double | 75%进度播放数 |
| play_over | double | 播放完成数 |
| play_over_rate | double | 完播率 |
| dy_like | double | 点赞次数 |
| dy_comment | double | 评论次数 |
| dy_share | double | 分享次数 |
| dy_follow | double | 新增粉丝数 |
| ecp_convert_cnt | double | 转化数 |
| ecp_convert_rate | double | 转化率 |
| ecp_cpa_platform | double | 转化成本 |
| deep_convert_cnt | double | 深度转化次数 |
| deep_convert_rate | double | 深度转化率 |
| deep_convert_cost | double | 深度转化成本 |
| create_order_count | double | 直接下单订单数 |
| create_order_amount | double | 直接下单金额 |
| create_order_roi | double | 直接下单ROI |
| create_order_coupon_amount | double | 下单智能优惠券金额 |
| pay_order_count | double | 直接成交订单数 |
| pay_order_amount | double | 直接成交金额 |
| pay_order_coupon_amount | double | 成交智能优惠券金额 |
| pay_order_cost_per_order | double | 直接成交客单价 |
| prepay_order_count | double | 直接预售订单数 |
| prepay_order_amount | double | 直接预售金额 |
| prepay_and_pay_order_roi | double | 直接支付ROI |
| unfinished_estimate_order_gmv | double | 未完结直接预售订单预估金额 |
| qianchuan_first_order_cnt | double | 店铺首单新客人数 |
| qianchuan_first_order_rate | double | 店铺首单新客订单占比 |
| qianchuan_first_order_convert_cost | double | 店铺首单新客转化成本 |
| qianchuan_first_order_direct_pay_gmv | double | 店铺首单新客直接成交金额 |
| qianchuan_first_order_direct_pay_order_roi | double | 店铺首单新客直接支付ROI |
| qianchuan_first_order_roi30 | double | 店铺首单新客30天累计支付ROI |
| qianchuan_first_order_ltv30 | double | 店铺首单新客30天累计成交金额 |
| qianchuan_brand_first_order_cnt | double | 品牌首单新客数 |
| qianchuan_brand_first_order_rate | double | 品牌首单新客订单占比 |
| qianchuan_brand_first_order_convert_cost | double | 品牌首单新客转化成本 |
| qianchuan_brand_first_order_direct_pay_gmv | double | 品牌首单新客直接成交金额 |
| qianchuan_brand_first_order_direct_pay_order_roi | double | 品牌首单新客直接支付ROI |
| qianchuan_brand_first_order_roi30 | double | 品牌首单新客30天累计支付ROI |
| qianchuan_brand_first_order_ltv30 | double | 品牌首单新客30天累计成交金额 |
| qianchuan_author_first_order_cnt | double | 抖音号首单新客人数 |
| qianchuan_author_first_order_rate | double | 抖音号首单新客订单占比 |
| qianchuan_author_first_order_convert_cost | double | 抖音号首单新客转化成本 |
| qianchuan_author_first_order_direct_pay_gmv | double | 抖音号首单新客直接成交金额 |
| qianchuan_author_first_order_direct_pay_order_roi | double | 抖音号首单新客直接支付ROI |
| qianchuan_author_first_order_roi30 | double | 抖音号首单新客30天累计支付ROI |
| qianchuan_author_first_order_ltv30 | double | 抖音号首单新客30天累计成交金额 |
| qianchuan_effective_view_convert_count | double | 有效看播数 |
| qianchuan_effective_view_convert_rate | double | 有效看播率 |
| qianchuan_effective_view_cpa_platform | double | 有效看播成本 |
| qianchuan_commission_convert_cnt | double | 带佣转化数 |
| qianchuan_estimated_commission | double | 直接预估佣金收入 |
| qianchuan_estimated_commission_roi | double | 直接佣金ROI |
| luban_live_enter_cnt | double | 直播间观看人次 |
| luban_live_comment_cnt | double | 直播间评论次数 |
| luban_live_share_cnt | double | 直播间分享次数 |
| luban_live_gift_cnt | double | 直播间打赏次数 |
| luban_live_gift_amount | double | 直播间音浪收入 |
| luban_live_click_product_cnt | double | 直播间商品点击次数 |
| luban_live_slidecart_click_cnt | double | 直播间查看购物车次数 |
| live_watch_one_minute_count | double | 直播间超过1分钟观看人次 |
| live_fans_club_join_cnt | double | 直播间新加团人次 |
| attribution_convert_cnt | double | 转化数(计费时间) |
| attribution_convert_rate | double | 转化率(计费时间) |
| attribution_convert_cost | double | 转化成本(计费时间) |
| attribution_deep_convert_cnt | double | 深度转化数(计费时间) |
| attribution_deep_convert_rate | double | 深度转化率(计费时间) |
| attribution_deep_convert_cost | double | 深度转化成本(计费时间) |
| indirect_order_create_count_7days | double | 7日间接下单订单数 |
| indirect_order_create_gmv_7days | double | 7日间接下单金额 |
| indirect_order_prepay_count_7days | double | 7日间接预售订单数 |
| indirect_order_prepay_gmv_7days | double | 7日间接预售金额 |
| indirect_order_pay_count_7days | double | 7日间接成交订单数 |
| indirect_order_pay_gmv_7days | double | 7日间接成交金额 |
| indirect_order_unfinished_estimate_gmv_7days | double | 7日未完结间接预售订单预估金额 |
| all_order_create_roi_7days | double | 7日总下单ROI |
| all_order_pay_count_7days | double | 7日总成交订单数 |
| all_order_pay_gmv_7days | double | 7日总成交金额 |
| all_order_prepay_and_pay_roi_7days | double | 7日总支付ROI |

### 响应示例
```json
{
    "code": 0,
    "message": "OK",
    "data": {
        "ad_material_infos": [
            {
                "material_info": {
                    "material_type": "VIDEO",
                    "video_material": {
                        "video_id": "v0200ff00000xxxx",
                        "image_mode": "VIDEO_VERTICAL",
                        "cover_image": {
                            "height": 1280,
                            "width": 720,
                            "web_url": "https://example.com/image.jpg",
                            "id": "web.business.image/123456789"
                        },
                        "material_id": 123456789,
                        "video_duration": 15,
                        "title": "视频标题",
                        "source": "AWEME"
                    }
                },
                "metrics": {
                    "stat_cost": 100.50,
                    "show_cnt": 1000,
                    "click_cnt": 50,
                    "ctr": 0.05
                }
            }
        ],
        "page_info": {
            "page": 1,
            "page_size": 10,
            "total_page": 1,
            "total_number": 1
        },
        "request_id": "20240101123456789"
    }
}
```

## 官方文档链接
[https://open.oceanengine.com/labels/12/docs/1810701753348235](https://open.oceanengine.com/labels/12/docs/1810701753348235)