# 巨量广告 - 刷新Refresh Token

## 接口描述
Refresh_Token在有效期内，可以通过接口刷新Access_Token，刷新会同时获得新的AccessToken及RefreshToken并更新效期时间（不会影响已有授权关系），同时原Token也会失效，再次刷新需要使用本次刷新获取的新的RefreshToken。

> **重要提示：**
> Refresh_Token、Access_Token、auth_code失效后，只能通过重新申请授权获取，建议在调用Token相关接口时避免并发请求。

## 请求地址
```
POST https://ad.oceanengine.com/open_api/oauth2/refresh_token/
```

## 请求方法
**POST**

## Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Content-Type <font color="red">必填</font> | string | 请求消息类型，允许值：`application/json` |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| app_id <font color="red">必填</font> | number | 开发者申请的应用APP_ID，可通过【应用管理】界面查看 |
| secret <font color="red">必填</font> | string | 开发者应用的私钥Secret，可通过【应用管理】界面编辑应用查看<br>• 传入app_id与secret需对应，及同一应用下信息 |
| refresh_token <font color="red">必填</font> | string | 刷新token，从「获取Access Token」和「刷新Access Token」的返回结果中得到，刷新后会过期，请及时保存最新的token |

## 请求示例
```python
import requests

open_api_url_prefix = "https://ad.oceanengine.com/open_api/"
uri = "oauth2/refresh_token/"
url = open_api_url_prefix + uri
data = {
    "app_id": 0,
    "secret": "xxx",
    "refresh_token": "xxx"
}
rsp = requests.post(url, json=data)
rsp_data = rsp.json()
return rsp_data
```

## 应答字段
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ access_token | string | 用于接口访问验证权限的Access_Token |
| ├─ refresh_token | string | Refresh_Token，刷新Token，用于获取新的access_token和refresh_token |
| ├─ expires_in | number | Access_Token剩余有效时间，单位（秒） |
| ├─ refresh_token_expires_in | number | Refresh_Token剩余有效时间，单位（秒） |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {}
}
```

## 官方文档链接
[刷新Refresh Token](https://open.oceanengine.com/labels/7/docs/1696710506097679?origin=left_nav)