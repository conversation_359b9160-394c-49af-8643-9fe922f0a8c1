# 巨量广告 - 获取项目列表

## 接口描述
获取巨量引擎投放版 项目列表

## 请求地址
```
GET https://ad.oceanengine.com/open_api/v3.0/project/list/
```

## 请求方法
**GET Request-Query**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权Access-Token，获取方法见API文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主ID |
| fields | string | 可选 | 查询字段集合，支持逗号分隔的字段名列表<br/>可参考应答参数返回的字段 |
| filtering | object | 可选 | 过滤条件，支持对字段和数据的条件进行过滤操作 |
| └─ ids | number[] | 可选 | 按广告组项目ID过滤，范围限制1-100 |
| └─ delivery_mode | string | 可选 | 按投放模式过滤，允许值：<br/>• `MANUAL` 手动投放<br/>• `PROCEDURAL` 自动投放 |
| └─ delivery_type | string | 可选 | 按投放类型过滤，允许值：<br/>• `NORMAL` 常规投放<br/>• `DURATION` 间断投放（搜索广告专享，仅限PC端操作） |
| └─ landing_type | string | 可选 | 按落地页类型过滤，允许值：<br/>• `SEARCH` - 如果需要同步优化代码中的_vid_type = SEARCH<br/>• `SHOP` - 如果需要同步拉新广告"转化"且此时填入landing_type字段<br/>• `ALL` - 所有条件下，仅对针对于拉新项目周期设置选项<br/>• `APP` 应用推广<br/>• `LINK` 销售线索收集<br/>• `MICRO_GAME` 小游戏<br/>• `MICRO_APP` 小程序<br/>• `QUICK_APP` 快应用<br/>• `NATIVE_ACTION` 原生动作<br/>• `DPA` 商品目录 |
| └─ app_promotion_type | string | 可选 | 子推广类型，允许值：<br/>• `DOWNLOAD` 应用下载<br/>• `LAUNCH` 应用拉活<br/>• `RESERVE` 应用预约 |
| └─ marketing_goal | string | 可选 | 营销目标，允许值：<br/>• `VIDEO_AND_IMAGE` 视频与图片<br/>• `LIVE` 直播 |
| └─ ad_type | string | 可选 | 广告类型，允许值：<br/>• `ALL` 所有广告<br/>• `SEARCH` 搜索广告 |
| └─ name | string | 可选 | 项目名称，支持模糊查询<br/>（例如填写长度大于50个字符，可能导致超时的匹配返回结果过多） |
| └─ status | string | 可选 | 项目状态过滤，允许值：<br/>• `PROJECT_STATUS_ENABLE` 启用<br/>• `PROJECT_STATUS_DISABLE` 暂停<br/>• `PROJECT_STATUS_DELETE` 删除<br/>• `PROJECT_STATUS_ALL` 所有项目<br/>• `PROJECT_STATUS_ARCHIVE` 归档项目<br/>• `PROJECT_STATUS_BUDGET_EXCEED` 预算超限项目<br/>• `PROJECT_STATUS_BUDGET_PRE_OFFLINE_BUDGET` 预算即将超限项目<br/>• `PROJECT_STATUS_NOT_START` 未开始投放<br/>• `PROJECT_STATUS_DONE` 已完成<br/>• `PROJECT_STATUS_ALL_AVAILABLE` 所有可用项目 |
| └─ status_first | string | 可选 | 项目一级状态过滤，允许值：<br/>• `PROJECT_STATUS_DELETE` 已删除<br/>• `PROJECT_STATUS_DONE` 已完成<br/>• `PROJECT_STATUS_ENABLE` 启用<br/>• `PROJECT_STATUS_DISABLE` 暂停<br/>不可使用变量时状态：不在过滤项下 |
| └─ status_second | string | 可选 | 项目二级状态过滤，允许值：<br/>• `PROJECT_STATUS_STOP` 已暂停<br/>• `PROJECT_STATUS_BUDGET_EXCEED` 预算超限项目<br/>• `PROJECT_STATUS_BUDGET_PRE_OFFLINE_BUDGET` 已完成超限预算计划<br/>• `PROJECT_STATUS_OFFLINE_BALANCE` 余额不足项目<br/>• `PROJECT_STATUS_BUDGET_BELOW_DELIVERY_BUDGET` 预算低于投放预算项目<br/>• `PROJECT_STATUS_REACH_DAILY_DELIVERY` 系统自动暂停该项目<br/>`status_first = PROJECT_STATUS_DISABLE` 时状态为有效 |
| └─ project_create_time | string | 可选 | 项目创建时间，格式：yyyy-MM-dd，表示对应日期的筛选表达式 |
| └─ project_modify_time | string | 可选 | 项目修改时间，格式：yyyy-MM-dd，表示对应日期的筛选表达式 |
| └─ pricing | string | 可选 | 按计费方式过滤 |
| └─ inventory_type | string | 可选 | 按库存类型过滤，允许值：<br/>• `INVENTORY_FEED` 今日头条<br/>• `INVENTORY_VIDEO_FEED` 西瓜视频<br/>• `INVENTORY_AWEME_FEED` 抖音信息流<br/>• `INVENTORY_TOMATO_NOVEL` 番茄小说<br/>• `INVENTORY_UNION_SLOT` 穿山甲<br/>• `UNION_BOUTIQUE_GAME` chaosys精品游戏<br/>• `INVENTORY_HOOKED_AGGREGATE` 住小帮<br/>• `INVENTORY_SEARCH` 搜索广告 |
| └─ platform | string | 可选 | 按平台过滤，允许值：<br/>• `IOS`<br/>• `ANDROID` |
| └─ budget_group_id | number | 可选 | 按预算组ID过滤，仅支持单个输入，设定能后自动启用预算组，如需使用预算组选项需【联系客户经理】来启用操作账户ID |
| └─ blue_flow_package_setting | string | 可选 | 是否开启流量包设定，允许值：<br/>• `ON`<br/>• `OFF` |
| └─ star_delivery_type | string | 可选 | 达人为主广告投放项目，允许值：<br/>• `STAR_DELIVERY` 达人广告投放项目<br/>• `NOT_STAR_DELIVERY` 非达人广告投放项目 |
| └─ star_auto_delivery_switch | string | 可选 | 达人广告投放自动化开关，允许值：<br/>• `ON` 开启<br/>• `OFF` 关闭<br/><br/>注意：需要全自动化开启，需要条件不满足时设置star_delivery_type参数 |
| page | number | 可选 | 页码数值，默认值：1，pageSize范围为[1,99999] |
| page_size | number | 可选 | 页面大小数值，默认值：10，page_size范围为[1,100] |

### 可查询字段（fields）说明
- `id` - 广告组ID
- `name` - 广告组名称
- `advertiser_id` - 广告主ID  
- `budget` - 预算金额
- `budget_mode` - 预算类型
- `status` - 广告组状态
- `opt_status` - 投放状态
- `delivery_mode` - 投放模式
- `delivery_type` - 投放类型
- `campaign_type` - 广告组类型
- `landing_type` - 落地页类型
- `marketing_goal` - 营销目标
- `marketing_scene` - 营销场景
- `promotion_way` - 推广方式
- `pricing` - 出价方式
- `speed_mode` - 投放速度
- `schedule_type` - 投放时间类型
- `schedule_time` - 投放时间段
- `create_time` - 创建时间
- `modify_time` - 修改时间
- `operation_status` - 操作状态
- `audience_package` - 定向包
- `auto_manage_budget` - 自动管理预算
- `budget_optimize_switch` - 预算优化开关
- `budget_optimize_goal` - 预算优化目标
- `roi_goal` - ROI目标
- `deep_bid_type` - 深度出价类型
- `inventory_type` - 广告库存类型
- `union_bid_type` - 穿山甲出价类型
- `search_bid_ratio` - 搜索出价比例
- `feed_delivery_search` - 信息流投放搜索

## 请求示例

```python
# coding: utf-8
import json
import requests

from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/project/list/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc, path, params, query, fragment = \
        urlparse("https://ad.oceanengine.com/open_api/v3.0/project/list/")
    
    return urlunparse((scheme, netloc, path, params, query, fragment))

def get():
    args = {
        "advertiser_id": 123456789,
        "filtering": {
            "status": "PROJECT_STATUS_ENABLE",
            "delivery_mode": "MANUAL",
            "marketing_goal": "VIDEO_AND_IMAGE",
            "project_create_time": "2023-01-01"
        },
        "fields": "id,name,budget,status,marketing_goal,delivery_mode",
        "page": 1,
        "page_size": 50
    }
    
    headers = {"Access-Token": ACCESS_TOKEN}
    
    # Convert filtering to JSON string for URL encoding  
    if 'filtering' in args:
        args['filtering'] = json.dumps(args['filtering'])
    
    query_string = urlencode(args)
    url = build_url(PATH, query_string)
    
    rsp = requests.get(url, headers=headers)
    rsp_data = rsp.json()
    return rsp_data

if __name__ == "__main__":
    rsp_data = get()
    print(json.dumps(rsp_data, ensure_ascii=False, indent=2))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | object | 返回数据 |
| ├─ list | object[] | 广告组列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ id | number | 广告组ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ name | string | 广告组名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_id | number | 广告主ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget | number | 预算金额，单位：分 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget_mode | string | 预算类型<br/>• `BUDGET_MODE_DAY` 日预算<br/>• `BUDGET_MODE_TOTAL` 总预算 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ status | string | 广告组状态<br/>• `PROJECT_STATUS_ENABLE` 启用<br/>• `PROJECT_STATUS_DISABLE` 暂停<br/>• `PROJECT_STATUS_DELETE` 删除 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ opt_status | string | 投放状态<br/>• `PROJECT_STATUS_ENABLE` 启用<br/>• `PROJECT_STATUS_DISABLE` 暂停<br/>• `PROJECT_STATUS_NOT_START` 未开始<br/>• `PROJECT_STATUS_DONE` 已完成<br/>• `PROJECT_STATUS_OFFLINE_BALANCE` 账户余额不足<br/>• `PROJECT_STATUS_OFFLINE_BUDGET` 预算不足 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ delivery_mode | string | 投放模式<br/>• `MANUAL` 手动投放<br/>• `PROCEDURAL` 自动投放 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ delivery_type | string | 投放类型<br/>• `NORMAL` 常规投放<br/>• `DURATION` 间断投放 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ campaign_type | string | 广告组类型<br/>• `NORMAL` 常规广告<br/>• `SEARCH` 搜索广告<br/>• `iOS14` iOS14专用广告 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ landing_type | string | 落地页类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ marketing_goal | string | 营销目标<br/>• `VIDEO_AND_IMAGE` 视频与图片<br/>• `LIVE` 直播 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ marketing_scene | string | 营销场景<br/>• `FEED` 通用推广<br/>• `SEARCH` 搜索推广<br/>• `UNION` 穿山甲推广<br/>• `UNIVERSAL` 全域推广 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_way | string | 推广方式<br/>• `STANDARD` 专业推广<br/>• `SIMPLE` 极速推广 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ pricing | string | 出价方式<br/>• `PRICING_CPC` 点击付费<br/>• `PRICING_CPM` 展示付费<br/>• `PRICING_CPA` 转化付费<br/>• `PRICING_CPV` 播放付费<br/>• `PRICING_OCPM` 目标转化出价<br/>• `PRICING_OCPC` 目标点击出价 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ speed_mode | string | 投放速度<br/>• `SPEED_MODE_STANDARD` 标准投放<br/>• `SPEED_MODE_FAST` 加速投放<br/>• `SPEED_MODE_SMOOTH` 均匀投放 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ schedule_type | string | 投放时间类型<br/>• `SCHEDULE_FROM_NOW` 从现在开始一直投放<br/>• `SCHEDULE_START_END` 设置开始和结束时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ schedule_time | string | 投放时间段配置 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ create_time | string | 创建时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ modify_time | string | 修改时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ operation_status | string | 操作状态 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ audience_package | object | 定向包信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ auto_manage_budget | boolean | 自动管理预算 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget_optimize_switch | string | 预算优化开关 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ budget_optimize_goal | string | 预算优化目标 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ roi_goal | number | ROI目标 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ deep_bid_type | string | 深度出价类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ inventory_type | array | 广告库存类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ union_bid_type | string | 穿山甲出价类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ search_bid_ratio | number | 搜索出价比例 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ feed_delivery_search | string | 信息流投放搜索 |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 当前页 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ page_size | number | 页面大小 |
| request_id | string | 请求日志ID |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "list": [
      {
        "id": 1234567890,
        "name": "测试项目广告组",
        "advertiser_id": 123456789,
        "budget": 500000,
        "budget_mode": "BUDGET_MODE_DAY",
        "status": "PROJECT_STATUS_ENABLE",
        "opt_status": "PROJECT_STATUS_ENABLE",
        "delivery_mode": "MANUAL",
        "delivery_type": "NORMAL",
        "campaign_type": "NORMAL",
        "landing_type": "SHOP",
        "marketing_goal": "VIDEO_AND_IMAGE",
        "marketing_scene": "FEED",
        "promotion_way": "STANDARD",
        "pricing": "PRICING_OCPM",
        "speed_mode": "SPEED_MODE_STANDARD",
        "schedule_type": "SCHEDULE_FROM_NOW",
        "schedule_time": "0100111010001110100011101000111",
        "create_time": "2023-01-15 10:30:00",
        "modify_time": "2023-01-16 14:20:00",
        "operation_status": "ENABLE",
        "audience_package": {
          "id": 123456,
          "name": "自定义定向包"
        },
        "auto_manage_budget": false,
        "budget_optimize_switch": "OFF",
        "budget_optimize_goal": "GOAL_CPM",
        "roi_goal": 200,
        "deep_bid_type": "DEEP_BID_MIN",
        "inventory_type": ["INVENTORY_FEED", "INVENTORY_VIDEO_FEED"],
        "union_bid_type": "UNION_BID_CPC",
        "search_bid_ratio": 100,
        "feed_delivery_search": "SEARCH_DISABLE"
      }
    ],
    "page_info": {
      "total_number": 50,
      "total_page": 5,
      "page": 1,
      "page_size": 10
    }
  },
  "request_id": "2023011610300012345"
}
```

## 说明
- 本API用于获取广告主下的广告组（项目）列表信息
- 支持多种过滤条件，包括状态、投放模式、营销目标等
- 返回字段包含广告组的基本信息、投放配置、优化设置等
- 推荐使用fields参数指定需要的字段，避免返回过多不必要的数据
- 分页查询支持，单次最多返回100条数据

## 官方文档链接
[获取项目列表-巨量广告升级版](https://open.oceanengine.com/labels/7/docs/1696710537994247)