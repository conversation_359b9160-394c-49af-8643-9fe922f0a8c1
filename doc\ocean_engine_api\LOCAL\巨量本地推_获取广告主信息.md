# 巨量本地推 - 获取广告主信息

## 接口描述
获取广告主账户详细信息，可指定fields查询所需元素。

> **注意：** 目前上线了新版的代理商鉴权，如果查询报"No Permission"错误，请前往代理商一站式平台为账号申请对应的权限。

**特别说明：** 对于入海广告主，在应答中将不返回以下字段（brand, promotion_area, promotion_center_province, promotion_center_city）

## 请求地址
```
https://api.oceanengine.com/open_api/2/advertiser/info/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Access-Token | string | 授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| advertiser_ids | number[] | 广告主ID集合（如果包含没有访问权限的ID,将返回no permission error）<br/>取值范围：1-100 |
| fields | string[] | 查询字段集合，默认:查询所有。字段详见下方response字段定义<br/>允许值：id、name、role、status、address、reason、license_url、license_no、license_province、license_city、company、brand、promotion_area、promotion_center_province、promotion_center_city、industry、create_time、note |

## 请求示例
```python
def get_advertiser_info():
    import requests
    
    open_api_url_prefix = "https://api.oceanengine.com/open_api/"
    uri = "2/advertiser/info/"
    url = open_api_url_prefix + uri
    params = {
        "advertiser_ids": [0],
        "fields": ["id", "name", "status"]
    }
    headers = {"Access-Token": "xxx"}
    rsp = requests.get(url, json=params, headers=headers)
    rsp_data = rsp.json()
    return rsp_data
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ id | number | 广告主ID |
| ├─ name | string | 账户名 |
| ├─ role | string | 角色，详见【附录-广告主角色】 |
| ├─ status | string | 状态，详见【附录-广告主状态】 |
| ├─ note | string | 备注 |
| ├─ address | string | 地址 |
| ├─ license_url | string | 执照预览地址（链接默认1小时内有效） |
| ├─ license_no | string | 执照编号 |
| ├─ license_province | string | 执照省份 |
| ├─ license_city | string | 执照城市 |
| ├─ company | string | 公司名 |
| ├─ brand | string | 经营类别 |
| ├─ promotion_area | string | 运营区域 |
| ├─ promotion_center_province | string | 运营省份 |
| ├─ promotion_center_city | string | 运营城市 |
| ├─ first_industry_name | string | 一级行业名称（新版） |
| ├─ second_industry_name | string | 二级行业名称（新版） |
| ├─ reason | string | 审核拒绝原因 |
| ├─ create_time | string | 创建时间 |
| └─ request_id | string | 请求接口日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": [{
    "id": *********,
    "name": "示例广告主账户",
    "role": "ADVERTISER",
    "status": "STATUS_ENABLE",
    "address": "北京市朝阳区某某街道123号",
    "license_url": "https://example.com/license.jpg",
    "license_no": "110000000000001",
    "license_province": "北京市",
    "license_city": "朝阳区",
    "company": "示例科技有限公司",
    "brand": "互联网服务",
    "promotion_area": "全国",
    "promotion_center_province": "北京市",
    "promotion_center_city": "朝阳区",
    "first_industry_name": "互联网",
    "second_industry_name": "电子商务",
    "reason": "",
    "create_time": "2023-01-01 10:00:00",
    "note": "示例备注信息"
  }],
  "request_id": "20240101*********abcdef"
}
```

## 常见问题
- [使用一级代理商的access_token能否查询二级代理商广告主信息呢？](https://ad.oceanengine.com/athena/faq/index.html?plat_id=7&id=418)

## 官方文档链接
[获取广告主信息](https://open.oceanengine.com/labels/37/docs/1696710508983311?origin=left_nav)