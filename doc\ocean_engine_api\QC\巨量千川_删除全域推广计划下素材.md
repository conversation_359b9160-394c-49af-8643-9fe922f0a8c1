# 巨量千川 - 删除全域推广计划下素材

## 接口概述
删除全域推广计划下素材

## 请求信息

### 请求地址
```
POST https://api.oceanengine.com/open_api/v1.0/qianchuan/uni_promotion/ad/material/delete/
```

### 请求头
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Access-Token | string | 是 | 授权access_token，可以通过【获取Access token】接口获取 |
| Content-Type | string | 是 | 请求消息类型，允许值：application/json |

### 请求参数
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| advertiser_id | number | 是 | 广告主ID |
| ad_id | number | 是 | 全域推广计划ID |
| material_ids | number[] | 是 | 待删除素材ID列表，最大支持100个 |

### 请求示例
```python
# coding=utf-8
import json
import requests

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/uni_promotion/ad/material/delete/"

def post(json_str):
    url = f"https://api.oceanengine.com{PATH}"
    args = json.loads(json_str)
    headers = {
        "Access-Token": ACCESS_TOKEN,
        "Content-Type": "application/json"
    }
    rsp = requests.post(url, headers=headers, json=args)
    return rsp.json()

if __name__ == '__main__':
    my_args = json.dumps({
        "advertiser_id": 123456789,
        "ad_id": 987654321,
        "material_ids": [123456789, 987654321]
    })
    print(post(my_args))
```

## 响应信息

### 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码 |
| message | string | 返回信息 |
| data | object | 返回数据 |
| request_id | string | 请求日志id |

### 响应示例
```json
{
    "code": 0,
    "message": "OK",
    "data": {},
    "request_id": "20240101123456789"
}
```

## 官方文档链接
[https://open.oceanengine.com/labels/12/docs/1804363891396633](https://open.oceanengine.com/labels/12/docs/1804363891396633)