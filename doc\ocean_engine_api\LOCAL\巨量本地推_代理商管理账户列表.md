# 巨量本地推 - 代理商管理账户列表

## 接口描述
获取代理商管理的账户列表。如需获取二级代理商相关账户信息，可使用agent/child_agent/select/接口查询代理商下的二级代理商账户，传递二级代理商id调用此接口即可获得。

**注意事项：**
- 查看广告主ID的详细信息请参考广告主信息接口
- 当请求数据量超过10000时，请使用cursor+count的分页方式请求数据

## 请求地址
```
https://api.oceanengine.com/open_api/2/agent/advertiser/select/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Access-Token | string | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| advertiser_id | number | 代理商ID |
| company_ids | number[] | 客户id列表，可用来过滤某个客户的adv |
| filtering | object | 过滤条件 |
| ├─ stat_cost_fen_gt | number | 消耗统计周期内的消耗金额，单位分<br/>注意：支持过滤数据统计周期内消耗大于xx的adv |
| ├─ cost_period | string | 消耗统计周期，允许值：<br/>• TODAY：今天<br/>• YESTERDAY：昨天<br/>• LAST_7_DAYS：最近7天，不包含当天<br/>• LAST_15_DAYS：最近15天，不包含当天<br/>• LAST_30_DAYS：最近30天，不包含当天 |
| ├─ advertiser_ids | number[] | 需要过滤广告主id列表，最多支持500个adv |
| ├─ create_start_time | string | 账户创建时间筛选的起始时间，精确到秒，格式yyyy-MM-dd HH:mm:ss |
| ├─ create_end_time | string | 账户创建时间筛选的结束时间，精确到秒，格式yyyy-MM-dd HH:mm:ss |
| ├─ total_balance_gt | number | 账户总余额大于，单位分 |
| ├─ nongrant_valid_balance_gt | number | 账户非赠款可用余额大于，单位分 |
| ├─ sale_id | number | 销售id |
| ├─ sale_name | string | 销售姓名 |
| └─ is_exist_sale | bool | 是否存在销售，不传不过滤<br/>true:返回有销售的记录<br/>false:返回没有销售的记录 |
| page | number | 页码，默认值：1<br/>注：page+page_size与cursor+count为两种分页方式<br/>page+page_size适用于获取数据记录数<10000的场景 |
| page_size | number | 页面数据量，默认值：100<br/>注：page+page_size与cursor+count为两种分页方式<br/>page+page_size适用于获取数据记录数<10000的场景 |
| cursor | number | 页码游标值，第一次拉取，无需入参<br/>注：page+page_size与cursor+count为两种分页方式<br/>cursor+count适用于获取数据记录数≥10000的场景 |
| count | number | 页面数据量，页面数据量<br/>注：page+page_size与cursor+count为两种分页方式<br/>cursor+count适用于获取数据记录数≥10000的场景 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/2/agent/advertiser/select/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == "__main__":
    advertiser_id = ADVERTISER_ID
    
    # Args in JSON format
    my_args = "{\"advertiser_id\": \"%s\"}" % (advertiser_id)
    print(get(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ list | int[] | 广告主ID列表 |
| ├─ account_source | string | 账号列表的账号类型。枚举值：<br/>• AD 广告主<br/>• STAR 星图<br/>• LUBAN 鲁班<br/>• DOMESTIC 入海<br/>• LOCAL 本地推 |
| ├─ page_info | json | 分页信息 |
| │&nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 页数 |
| │&nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| │&nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| │&nbsp;&nbsp;&nbsp;&nbsp;└─ total_page | number | 总页数 |
| ├─ cursor_page_info | json | 分页信息 |
| │&nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| │&nbsp;&nbsp;&nbsp;&nbsp;├─ has_more | number | 是否有下一页 |
| │&nbsp;&nbsp;&nbsp;&nbsp;├─ count | number | 页面数据量 |
| │&nbsp;&nbsp;&nbsp;&nbsp;└─ cursor | number | 下一次分页拉取的游标值 |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "list": [*************, *************, *************],
    "account_source": "LOCAL",
    "page_info": {
      "page": 1,
      "page_size": 100,
      "total_number": 3,
      "total_page": 1
    },
    "cursor_page_info": {
      "total_number": 3,
      "has_more": 0,
      "count": 3,
      "cursor": 0
    }
  },
  "request_id": "20230115143000123456"
}
```

## 官方文档链接
[代理商管理账户列表](https://open.oceanengine.com/labels/37/docs/****************?origin=left_nav)