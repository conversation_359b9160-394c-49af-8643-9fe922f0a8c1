# 巨量千川 - 查询账户累计积分

## 接口描述
账户累计积分

## 请求地址
```
GET https://api.oceanengine.com/open_api/v3.0/security/score_total/get/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主id |
| page | number | 可选 | 页码 |
| page_size | number | 可选 | 页面大小 |
| business_line | string | 必填 | 业务线，可选值：<br/>• `QIANCHUAN` 千川 |
| filtering | object | 可选 | 过滤器 |
| └─ year | string | 可选 | 积分年度 |
| └─ illegal_type | string | 可选 | 违规类型，可选值：<br/>• `ONECLASS` 一类违规（千川）<br/>• `TWOTHREECLASS` 二三类违规（千川） |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/security/score_total/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    # Args in JSON format
    my_args = json.dumps({
        "advertiser_id": 123456789,
        "page": 1,
        "page_size": 10,
        "business_line": "QIANCHUAN",
        "filtering": {
            "year": "2024",
            "illegal_type": "ONECLASS"
        }
    })
    print(get(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 第几页 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 分页大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total | number | 总条数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| ├─ score_info_list | object[] | 积分详情 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_id | number | 广告主id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ illegal_type | string | 违规类型，可选值：<br/>• `ONECLASS` 一类违规（千川）<br/>• `TWOTHREECLASS` 二三类违规（千川） |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ year | string | 积分年度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ score | number | 年分 |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "page_info": {
      "page": 1,
      "page_size": 10,
      "total": 2,
      "total_page": 1
    },
    "score_info_list": [
      {
        "advertiser_id": 123456789,
        "illegal_type": "ONECLASS",
        "year": "2024",
        "score": 85
      },
      {
        "advertiser_id": 123456789,
        "illegal_type": "TWOTHREECLASS",
        "year": "2024",
        "score": 90
      }
    ]
  },
  "request_id": "20240101123456789"
}
```

## 官方文档链接
[查询账户累计积分](https://open.oceanengine.com/labels/12/docs/1809254968749066)