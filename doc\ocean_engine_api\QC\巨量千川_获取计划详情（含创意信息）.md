# 获取计划详情（含创意信息）

## 接口描述
用于获取千川广告账户下计划的详细信息，包含创意相关信息。

## 请求地址
https://api.oceanengine.com/open_api/v1.0/qianchuan/ad/detail/get/

## 请求方法
GET

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Access-Token | string | 必填，授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| advertiser_id | number | 必填，千川广告账户ID |
| ad_id | number | 必填，计划ID |
| request_aweme_info | number | 是否包含抖音号信息，允许值：<br>• 0：不包含<br>• 1：包含，默认不返回 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "YOUR_ACCESS_TOKEN"
PATH = "/open_api/v1.0/qianchuan/ad/detail/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    return urlunparse(("https", "ad.oceanengine.com", path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == "__main__":
    # Args in JSON format
    my_args = "{\"advertiser_id\": ADVERTISER_ID,\"ad_id\": AD_ID,\"request_aweme_info\": 1}"
    print(get(my_args))
```

## 应答字段
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | object | json返回值 |
| └─ ad_id | number | 计划ID |
| └─ campaign_id | number | 广告组ID |
| └─ name | string | 计划名称 |
| └─ status | string | 计划投放状态 |
| └─ opt_status | string | 计划操作状态 |
| └─ ad_create_time | string | 计划创建时间 |
| └─ ad_modify_time | string | 计划更新时间 |
| └─ marketing_goal | string | 营销目标 |
| └─ marketing_scene | string | 广告类型 |
| └─ campaign_scene | string | 营销场景 |
| └─ lab_ad_type | string | 推广方式 |
| └─ product_info | object[] | 商品信息列表 |
| └─ └─ id | number | 商品ID |
| └─ └─ name | string | 商品名称 |
| └─ └─ img | string | 商品主图 |
| └─ └─ market_price | float | 原价 |
| └─ └─ discount_lower_price | float | 折扣价区间最小值 |
| └─ └─ discount_higher_price | float | 折扣价区间最大值 |
| └─ creative_info | object[] | 创意信息列表 |
| └─ └─ creative_id | number | 创意ID |
| └─ └─ creative_title | string | 创意标题 |
| └─ └─ creative_type | string | 创意类型 |
| └─ └─ image_info | object[] | 图片素材信息 |
| └─ └─ └─ image_id | string | 图片ID |
| └─ └─ └─ image_url | string | 图片URL |
| └─ └─ video_info | object | 视频素材信息 |
| └─ └─ └─ video_id | string | 视频ID |
| └─ └─ └─ video_url | string | 视频URL |
| └─ └─ └─ video_cover_url | string | 视频封面URL |
| └─ └─ └─ duration | number | 视频时长 |
| └─ delivery_setting | object | 投放设置 |
| └─ └─ external_action | string | 转化目标 |
| └─ └─ deep_external_action | string | 深度转化目标 |
| └─ └─ budget | float | 预算 |
| └─ └─ budget_mode | string | 预算类型 |
| └─ └─ cpa_bid | float | 转化出价 |
| └─ └─ start_time | string | 投放开始时间 |
| └─ └─ end_time | string | 投放结束时间 |
| └─ aweme_info | object[] | 抖音号信息 |
| └─ └─ aweme_id | number | 抖音ID |
| └─ └─ aweme_name | string | 抖音昵称 |
| └─ └─ aweme_show_id | string | 抖音号展示ID |
| └─ └─ aweme_avatar | string | 抖音号头像 |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "request_id": "202105111551080102121931483A043E53",
  "data": {
    "ad_id": 1699447403234343,
    "campaign_id": 1699447403234340,
    "name": "计划名称示例",
    "status": "ENABLE",
    "opt_status": "ENABLE",
    "ad_create_time": "2021-05-11 15:49:01",
    "ad_modify_time": "2021-05-11 16:49:01",
    "marketing_goal": "LIVE_PROM_GOODS",
    "marketing_scene": "FEED",
    "campaign_scene": "DAILY_SALE",
    "lab_ad_type": "NOT_LAB_AD",
    "product_info": [
      {
        "id": 1234567890,
        "name": "商品名称示例",
        "img": "https://example.com/product.jpg",
        "market_price": 100.00,
        "discount_lower_price": 80.00,
        "discount_higher_price": 90.00
      }
    ],
    "creative_info": [
      {
        "creative_id": 1699447403234344,
        "creative_title": "创意标题示例",
        "creative_type": "VIDEO",
        "video_info": {
          "video_id": "v123456789",
          "video_url": "https://example.com/video.mp4",
          "video_cover_url": "https://example.com/cover.jpg",
          "duration": 30
        }
      }
    ],
    "delivery_setting": {
      "external_action": "AD_CONVERT_TYPE_LIVE_SUCCESSORDER_ACTION",
      "budget": 500.0,
      "budget_mode": "BUDGET_MODE_DAY",
      "cpa_bid": 65.88,
      "start_time": "2021-05-11",
      "end_time": "2021-05-31"
    },
    "aweme_info": [
      {
        "aweme_id": 123456789,
        "aweme_name": "抖音昵称示例",
        "aweme_show_id": "dyid123456",
        "aweme_avatar": "https://example.com/avatar.jpg"
      }
    ]
  }
}
```

## 官方文档链接
https://open.oceanengine.com/labels/12/docs/1697467544882191?origin=left_nav