# 巨量千川 - 获取千川操作日志

## 接口描述
支持查询千川账户/计划维度操作日志。

**注意：开始时间和结束时间不可超过当前时间-180天**

## 请求地址
```
GET https://api.oceanengine.com/open_api/v1.0/qianchuan/tools/log_search/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主ID |
| object_id | number | 条件必填 | 千川计划id<br/>**注意：当object_type=AD时，必填** |
| object_type | string | 必填 | 可选值：<br/>• `AD` 计划操作日志<br/>• `ACCOUNT` 账户操作日志 |
| operator_id | number[] | 可选 | 操作人id |
| start_time | string | 可选 | 日志查询开始时间，格式 "2019-07-24 21:46:57"<br/>**注意：开始时间不可超过当前时间-180天** |
| end_time | string | 可选 | 日志查询结束时间，格式 "2019-07-24 21:46:57"<br/>**注意：结束时间不可超过当前时间-180天** |
| page | number | 可选 | 页码，默认值：`1` |
| page_size | number | 可选 | 页面大小，默认值：`10`，允许值：1~20 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/tools/log_search/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == "__main__":
    advertiser_id = ADVERTISER_ID
    object_id = OBJECT_ID
    object_type = OBJECT_TYPE
    operator_id_list = OPERATOR_ID
    operator_id = json.dumps(operator_id_list)
    start_time = START_TIME
    end_time = END_TIME
    page = PAGE
    page_size = PAGE_SIZE

    # Args in JSON format
    my_args = "{\"advertiser_id\": \"%s\", \"object_id\": \"%s\", \"object_type\": \"%s\", \"operator_id\": %s, \"start_time\": \"%s\", \"end_time\": \"%s\", \"page\": \"%s\", \"page_size\": \"%s\"}" % (advertiser_id, object_id, object_type, operator_id, start_time, end_time, page, page_size)
    print(get(my_args))
```

## 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 页数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| ├─ logs | object[] | 日志详情 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ content_title | string | 操作内容 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ object_type | string | 操作对象类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ object_id | number | 操作对象ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ object_name | string | 操作对象名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ create_time | string | 操作时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ content_log | string[] | 操作前后内容 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ operator_name | string | 操作人 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ operator_id | string | 操作人id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ opt_ip | string | 操作IP |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ log_id | string | 操作日志id |
| request_id | string | 请求日志id |

## 响应示例
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "page_info": {
      "page": 1,
      "page_size": 10,
      "total_number": 2,
      "total_page": 1
    },
    "logs": [
      {
        "content_title": "修改计划预算",
        "object_type": "AD",
        "object_id": 123456789,
        "object_name": "千川推广计划001",
        "create_time": "2024-01-15 10:30:00",
        "content_log": [
          "预算由100.00元修改为200.00元"
        ],
        "operator_name": "操作员姓名",
        "operator_id": "987654321",
        "opt_ip": "*************",
        "log_id": "log_20240115103000_001"
      },
      {
        "content_title": "暂停推广计划",
        "object_type": "AD",
        "object_id": 123456789,
        "object_name": "千川推广计划001",
        "create_time": "2024-01-15 14:20:00",
        "content_log": [
          "计划状态由启用修改为暂停"
        ],
        "operator_name": "操作员姓名",
        "operator_id": "987654321",
        "opt_ip": "*************",
        "log_id": "log_20240115142000_002"
      }
    ]
  },
  "request_id": "20240101123456789"
}
```

## 官方文档链接
[获取千川操作日志](https://open.oceanengine.com/labels/12/docs/1832813828161028?origin=left_nav)