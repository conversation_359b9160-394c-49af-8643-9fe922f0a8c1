# 巨量千川 - 获取计划下素材列表

## 接口概述
获取计划下的素材信息，包括素材的元信息，审核状态，关联创意ID，派生信息，是否删除等。

## 请求信息

### 请求地址
```
GET https://api.oceanengine.com/open_api/v1.0/qianchuan/ad/material/get/
```

### 请求头
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Access-Token | string | 是 | 授权access_token，可以通过【获取Access token】接口获取 |

### 请求参数
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| advertiser_id | number | 是 | 千川广告账户ID |
| ad_id | number | 是 | 计划ID |
| filtering | object | 是 | 过滤条件 |
| material_type | string | 是 | 素材类型：<br>- `IMAGE`：图片，图文<br>- `TITLE`：标题<br>- `LIVE_ROOM`：直播间画面<br>- `VIDEO`：视频 |
| image_mode | string[] | 否 | 素材样式，仅material_type=VIDEO/IMAGE时支持<br>**VIDEO时支持**：<br>- `VIDEO_LARGE`：横版视频<br>- `VIDEO_VERTICAL`：竖版视频<br>**IMAGE时支持**：<br>- `SMALL`：横版小图<br>- `LARGE`：横版大图<br>- `LARGE_VERTICAL`：竖版图片<br>- `CAROUSEL`：图文 |
| having_cost | string | 否 | 消耗情况，仅material_type=VIDEO/IMAGE时支持：<br>- `ALL`：全部<br>- `"YES"`：有消耗<br>默认查询全部 |
| video_source | string[] | 否 | 视频来源，仅material_type=VIDEO时支持：<br>- `AWEME`：抖音主页视频<br>- `E_COMMERCE`：本地上传<br>- `LIVE_HIGHLIGHT`：直播剪辑素材<br>- `BP`：巨量纵横共享素材<br>- `VIDEO_CAPTURE`：易拍APP共享素材<br>- `ARTHUR`：亚瑟共享素材<br>- `STAR`：星图&即合共享素材<br>- `TADA`：tada共享素材<br>- `CREATIVE_CENTER`：巨量创意PC共享素材<br>- `JIANYING`：剪映共享素材<br>- `JI_CHUANG`：即创共享素材 |
| analysis_type | string[] | 否 | 素材建议，仅material_type=VIDEO时支持：<br>- `CARRY_MATERIAL`：搬运风险素材<br>- `LOW_EFFICIENCY_MATERIAL`：低效素材<br>- `FIRST_PUBLISH_MATERIAL`：首发素材<br>- `SIMILAR_RISK_MATERIAL`：同质化素材<br>- `HIGH_QUALITY_MATERIAL`：优质素材<br>- `POOR_QUALITY_MATERIAL`：低质素材 |
| search_keyword | string | 否 | 搜索关键词，支持查询直播间/视频/标题/图片名称、直播间/视频/图片id |
| start_time | string | 条件必填 | 数据开始时间，注意：仅having_cost入参时，该筛选项生效 |
| end_time | string | 条件必填 | 数据结束时间，注意：仅having_cost入参时，该筛选项生效 |
| page | number | 否 | 页码，默认值：1 |
| page_size | number | 否 | 页面大小，允许值：10, 20, 50, 100，默认值：10 |
| order_type | string | 否 | 排序方式：<br>- `ASC`：升序<br>- `DESC`：降序（默认） |
| order_field | string | 否 | 排序字段，支持根据消耗等数据指标排序 |

### 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/ad/material/get/"

# 请求参数
advertiser_id = "ADVERTISER_ID"
ad_id = "AD_ID"
filtering = {
    "material_type": "VIDEO",
    "image_mode": ["VIDEO_VERTICAL"],
    "having_cost": "ALL",
    "video_source": ["AWEME"],
    "analysis_type": ["HIGH_QUALITY_MATERIAL"],
    "search_keyword": "",
    "start_time": "2024-01-01 00:00:00",
    "end_time": "2024-01-31 23:59:59"
}
page = 1
page_size = 10
order_type = "DESC"
order_field = "stat_cost"

# 构建请求URL
query_params = {
    "advertiser_id": advertiser_id,
    "ad_id": ad_id,
    "filtering": json.dumps(filtering),
    "page": page,
    "page_size": page_size,
    "order_type": order_type,
    "order_field": order_field
}

query_string = urlencode(query_params)
url = f"https://api.oceanengine.com{PATH}?{query_string}"

headers = {
    "Access-Token": ACCESS_TOKEN
}

response = requests.get(url, headers=headers)
print(response.json())
```

## 响应信息

### 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | json返回值 |
| ad_material_infos | object[] | 返回的素材信息列表 |
| material_info | object | 素材信息 |
| video_material | object | 视频素材 |
| video_id | string | 视频id |
| image_mode | string | 素材样式 |
| material_id | number | 素材id |
| cover_image | object | 视频封面图片 |
| height | number | 宽 |
| width | number | 高 |
| web_url | string | 图片链接 |
| id | string | 图片id |
| video_duration | number | 视频时长 |
| title | string | 视频标题 |
| source | string | 视频来源 |
| image_material | object | 图片素材 |
| images | object[] | 图片 |
| image_url | string | 图片链接 |
| music_url | string | 图文音乐播放链接 |
| description | string | 图文描述 |
| title_material | object | 标题素材 |
| room_material | object | 直播间画面用户信息 |
| aweme_avatar | string | 头像 |
| material_type | string | 素材类型 |
| material_delivery_type | string | 素材投放状态 |
| creative_ids | number[] | 关联的创意id |
| is_del | bool | 是否删除 |
| audit_status | string | 审核状态：<br>- `PASS`：审核通过<br>- `REJECT`：审核拒绝<br>- `IN_PROGRESS`：审核中 |
| is_auto_generate | number | 是否派生 |
| page_info | object | 分页结果 |
| page | number | 页码 |
| page_size | number | 页面大小 |
| total_page | number | 总页数 |
| total_num | number | 总数量 |
| request_id | string | 请求日志id |

### 响应示例
```json
{
    "code": 0,
    "message": "OK",
    "data": {
        "ad_material_infos": [
            {
                "material_info": {
                    "video_material": {
                        "video_id": "v0200ff00000xxxx",
                        "image_mode": "VIDEO_VERTICAL",
                        "material_id": 123456789,
                        "cover_image": {
                            "height": 720,
                            "width": 1280,
                            "web_url": "https://example.com/image.jpg",
                            "id": "web.business.image/123456789"
                        },
                        "video_duration": 15,
                        "title": "视频标题",
                        "source": "AWEME"
                    }
                },
                "material_type": "VIDEO",
                "material_delivery_type": "NORMAL",
                "creative_ids": [123456789],
                "is_del": false,
                "audit_status": "PASS",
                "is_auto_generate": 0
            }
        ],
        "page_info": {
            "page": 1,
            "page_size": 10,
            "total_page": 1,
            "total_num": 1
        },
        "request_id": "20240101123456789"
    }
}
```

## 业务错误码
| Code | Message | 解决方案 |
|------|---------|----------|
| 40000 | 未查询到对应记录 | 请先参考报错信息自行排查 |

## 官方文档链接
[https://open.oceanengine.com/labels/12/docs/1803705421766656](https://open.oceanengine.com/labels/12/docs/1803705421766656)