# 巨量千川 - 查看积分处置详情

## 接口概述
查看积分处置详情信息

## 请求信息

### 请求地址
```
GET https://api.oceanengine.com/open_api/v1.0/qianchuan/credit/dispose/detail/
```

### 请求头
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Access-Token | string | 是 | 授权access_token，可以通过【获取Access token】接口获取 |

### 请求参数
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| advertiser_id | number | 是 | 广告主ID |
| start_time | string | 否 | 开始时间，格式：yyyy-MM-dd |
| end_time | string | 否 | 结束时间，格式：yyyy-MM-dd |
| page | number | 否 | 页码，默认值：1 |
| page_size | number | 否 | 页面大小，允许值：10, 20, 50, 100，默认值：10 |

### 请求示例
```python
# coding=utf-8
import json
import requests
from six.moves.urllib.parse import urlencode

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/credit/dispose/detail/"

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode(args)
    url = f"https://api.oceanengine.com{PATH}?{query_string}"
    headers = {"Access-Token": ACCESS_TOKEN}
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    my_args = json.dumps({
        "advertiser_id": 123456789,
        "start_time": "2024-01-01",
        "end_time": "2024-01-31",
        "page": 1,
        "page_size": 10
    })
    print(get(my_args))
```

## 响应信息

### 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码 |
| message | string | 返回信息 |
| data | object | 返回数据 |
| dispose_details | object[] | 积分处置详情列表 |
| dispose_type | string | 处置类型 |
| dispose_time | string | 处置时间 |
| dispose_reason | string | 处置原因 |
| credit_impact | number | 积分影响 |
| page_info | object | 分页信息 |
| request_id | string | 请求日志id |

### 响应示例
```json
{
    "code": 0,
    "message": "OK",
    "data": {
        "dispose_details": [
            {
                "dispose_type": "PENALTY",
                "dispose_time": "2024-01-15 10:30:00",
                "dispose_reason": "违规处置原因",
                "credit_impact": -20
            }
        ],
        "page_info": {
            "page": 1,
            "page_size": 10,
            "total_page": 1,
            "total_num": 1
        },
        "request_id": "20240101123456789"
    }
}
```

## 官方文档链接
[https://open.oceanengine.com/labels/12/docs/1809255307204884](https://open.oceanengine.com/labels/12/docs/1809255307204884)