# 巨量本地推 - 枚举值

## 接口描述
巨量本地推平台使用与巨量广告平台相同的枚举值体系。本文档说明枚举值的使用方式和参考来源。

## 枚举值说明

巨量本地推平台的API接口枚举值与巨量广告平台**完全一致**
## 使用方式

## 1. 参考文档
请直接参考巨量广告平台的枚举值文档：
- 文档位置：`docs/api/AD/巨量广告_枚举值.md`
- 官方文档：[巨量广告枚举值](https://open.oceanengine.com/labels/7/docs/1696710760171535?origin=left_nav)


## 使用建议

1. **统一枚举处理**：使用与巨量广告平台相同的枚举值处理逻辑
2. **代码复用**：枚举值定义可以在广告和本地推平台之间复用
3. **更新同步**：枚举值更新会同步到广告平台文档中
4. **类型安全**：建议在代码中使用常量或枚举类型定义这些值

## 重要说明

- 本地推平台与广告平台**共享相同的枚举值定义**
- 业务逻辑和验证规则可以复用
- 新增的枚举值会同步更新到广告平台文档中
- 部分本地推特有的业务场景可能会有额外的枚举值，详见官方文档

## 官方文档链接
由于本地推使用广告平台的枚举值，请参考：
[巨量广告枚举值](https://open.oceanengine.com/labels/7/docs/1696710760171535?origin=left_nav)