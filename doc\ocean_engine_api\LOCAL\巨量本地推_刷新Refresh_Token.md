# 巨量本地推 - 刷新Refresh Token

## 接口描述
Refresh_Token在有效期内，可以通过接口刷新Access_Token，刷新会同时获得新的AccessToken及RefreshToken并更新效期时间（不会影响已有授权关系），同时原Token也会失效，再次刷新需要使用本次刷新获取的新的RefreshToken。

> **注意：** Refresh_Token、Access_Token、auth_code失效后，只能通过重新申请授权获取，建议在调用Token相关接口时避免并发请求。

## 请求地址
```
https://api.oceanengine.com/open_api/oauth2/refresh_token/
```

## 请求方法
**POST**

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Content-Type | string | 请求消息类型，允许值：application/json |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| app_id | number | 必填 | 开发者申请的应用APP_ID，可通过【应用管理】界面查看 |
| secret | string | 必填 | 开发者应用的私钥Secret，可通过【应用管理】界面编辑应用查看<br/>• 传入app_id与secret需对应，及同一应用下信息 |
| refresh_token | string | 必填 | 刷新token，从「获取Access Token」和「刷新Access Token」的返回结果中得到，刷新后会过期，请及时保存最新的token |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/oauth2/refresh_token/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def post(json_str):
    # type: (str) -> dict
    """
    Send POST request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    url = build_url(PATH)
    args = json.loads(json_str)
    headers = {
        "Access-Token": ACCESS_TOKEN,
        "Content-Type": "application/json",
    }
    rsp = requests.post(url, headers=headers, json=args)
    return rsp.json()

if __name__ == "__main__":
    app_id = APP_ID
    secret = SECRET
    refresh_token = REFRESH_TOKEN
    
    # Args in JSON format
    my_args = "{\"app_id\": \"%s\", \"secret\": \"%s\", \"refresh_token\": \"%s\"}" % (app_id, secret, refresh_token)
    print(post(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ access_token | string | 用于接口访问验证权限的Access_Token |
| ├─ refresh_token | string | Refresh_Token，刷新Token，用于获取新的access_token和refresh_token |
| ├─ expires_in | number | Access_Token剩余有效时间，单位（秒） |
| ├─ refresh_token_expires_in | number | Refresh_Token剩余有效时间，单位（秒） |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "access_token": "新的访问令牌",
    "refresh_token": "新的刷新令牌",
    "expires_in": 7200,
    "refresh_token_expires_in": 2592000,
    "request_id": "20240712123456789abcdef"
  }
}
```

## 官方文档链接
[刷新Refresh Token](https://open.oceanengine.com/labels/37/docs/1696710506097679?origin=left_nav)