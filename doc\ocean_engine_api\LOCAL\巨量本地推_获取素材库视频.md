# 巨量本地推 - 获取素材库视频

## 接口描述
获取素材库视频

## 请求地址
`GET https://api.oceanengine.com/open_api/v3.0/local/file/video/get/`

## 请求方法
GET

## 请求Header

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| local_account_id | number | 必填 | 本地推广告账户ID |
| filtering | object | 必填 | 过滤器 |
| ├─ search_key_word | string | 可选 | 根据视频素材名称或素材ID筛选 |
| ├─ image_mode | string[] | 可选 | 素材类型，允许值：<br/>• `IMAGE_MODE_VIDEO` - 横版视频<br/>• `IMAGE_MODE_VIDEO_VERTICAL` - 竖版视频 |
| ├─ material_source | string[] | 可选 | 素材来源，允许值：<br/>• `BP_PLATFORM` - 巨量引擎工作平台共享视频<br/>• `CREATIVE_AIGC` - 即创<br/>• `LOCAL_ADS_UPLOAD` - 本地上传<br/>• `STAR` - 星图平台<br/>• `MAPI` - MAPI上传 |
| ├─ analysis_type | string[] | 可选 | 评估类型，允许值：<br/>• `FIRST_PUBLISH` - 首发<br/>• `FIRST_PUBLISH_AND_HIGH_QUALITY` - 首发&优质<br/>• `HIGH_QUALITY` - 优质 |
| ├─ start_time | string | 可选 | 根据视频上传时间进行过滤的起始时间，与end_time搭配使用，格式：`yyyy-MM-dd HH:mm:ss` |
| ├─ end_time | string | 可选 | 根据视频上传时间，进行过滤的截止时间，与start_time搭配使用，格式：`yyyy-MM-dd HH:mm:ss` |
| ├─ is_filter_unqualified | bool | 可选 | 是否过滤低质素材，允许值：<br/>• `false` - 不过滤<br/>• `true` - 过滤（默认值） |
| ├─ order_field | string | 可选 | 排序字段，允许值：<br/>• `CONVERSION_COST` - 转化成本<br/>• `CONVERSION_RATE` - 转化率<br/>• `CREATE_TIME` - 创建时间（默认值）<br/>• `CTR` - 点击率<br/>• `DURATION` - 视频时长<br/>• `STAT_COST` - 消耗 |
| ├─ order_type | string | 可选 | 排序顺序，允许值：<br/>• `ASC` - 升序<br/>• `DESC` - 降序（默认值） |
| page | number | 可选 | 页码，默认值：`1` |
| page_size | number | 可选 | 页面大小，默认值：`20`，最大值：`100` |

## 请求示例

```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/local/file/video/get/"

def build_url(path, query=""):
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    local_account_id = *********  # 替换为实际的本地推广告账户ID
    my_args = "{\"local_account_id\": \"%s\", \"filtering\": {\"is_filter_unqualified\": \"true\"}, \"order_field\": \"CREATE_TIME\", \"order_type\": \"DESC\", \"page\": \"1\", \"page_size\": \"20\"}" % (local_account_id)
    print(get(my_args))
```

## 响应参数

| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | 返回数据 |
| ├─ video_list | object[] | 素材库视频列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ video_id | string | 视频ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ material_id | number | 素材id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ signature | string | 视频md5 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ video_name | string | 视频名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ video_url | string | 视频地址，链接有效期：1小时 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ poster_url | string | 视频首帧截图 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ material_properties | string[] | 素材标签，枚举值：<br/>• `COPY` - 搬运风险<br/>• `FIRST_PUBLISH` - 首发<br/>• `HIGH_QUALITY` - 优质<br/>• `LOW_QUALITY` - 低质<br/>• `SIMILAR` - 同质化风险 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ image_mode | string | 视频类型，枚举值：<br/>• `IMAGE_MODE_VIDEO` - 横版视频<br/>• `IMAGE_MODE_VIDEO_VERTICAL` - 竖版视频 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ duration | double | 视频时长 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ source | string | 视频来源，枚举值：<br/>• `BP_PLATFORM` - 巨量引擎工作平台共享视频<br/>• `CREATIVE_AIGC` - 即创<br/>• `LOCAL_ADS_UPLOAD` - 本地上传<br/>• `STAR` - 星图平台<br/>• `MAPI` - MAPI接口上传 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ create_time | string | 素材的上传时间，格式：`yyyy-mm-dd HH:mm:ss` |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 页码 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| request_id | string | 请求日志id |

## 响应示例

```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "video_list": [
      {
        "video_id": "v*********0*********0",
        "material_id": 2001,
        "signature": "abc123def456ghi789",
        "video_name": "优质产品展示视频",
        "video_url": "https://example.com/video123.mp4",
        "poster_url": "https://example.com/poster123.jpg",
        "material_properties": ["FIRST_PUBLISH", "HIGH_QUALITY"],
        "image_mode": "IMAGE_MODE_VIDEO_VERTICAL",
        "duration": 15.5,
        "source": "LOCAL_ADS_UPLOAD",
        "create_time": "2023-06-01 10:30:00"
      },
      {
        "video_id": "v09876543210987654321",
        "material_id": 2002,
        "signature": "def456ghi789abc123",
        "video_name": "品牌宣传横版视频",
        "video_url": "https://example.com/video456.mp4",
        "poster_url": "https://example.com/poster456.jpg",
        "material_properties": ["HIGH_QUALITY"],
        "image_mode": "IMAGE_MODE_VIDEO",
        "duration": 30.0,
        "source": "BP_PLATFORM",
        "create_time": "2023-06-02 14:20:15"
      }
    ],
    "page_info": {
      "page": 1,
      "page_size": 20,
      "total_page": 5,
      "total_number": 100
    }
  },
  "request_id": "20240101*********abcdef"
}
```

## 官方文档链接
https://open.oceanengine.com/labels/37/docs/1808613640441882?origin=left_nav