# 巨量广告 - 操作日志查询

## 接口描述
可查询巨量广告、巨量本地推、巨量千川后台操作日志，默认查询最近7天的数据，最多查询跨度为一个月。

**注意：当前一次最多支持查询2000条日志数据，超过2000条返回的分页信息可能存在不准确的问题，建议您缩短查询时间（7天以内）。**

## 请求地址
```
GET https://ad.oceanengine.com/open_api/2/tools/log_search/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 业务账户ID |
| object_id | number[] | 可选 | 操作对象ID，可以为campaign_id、ad_id、creative_id等业务下对应对象ID<br/>长度限制：[1,20] |
| start_time | string | 可选 | 日志查询开始时间<br/>格式：yyyy-mm-dd HH:MM:SS |
| end_time | string | 可选 | 日志查询结束时间<br/>格式：yyyy-mm-dd HH:MM:SS |
| page | number | 可选 | 页码，默认值：`1` |
| page_size | number | 可选 | 页面大小，默认值：`10`，最大值：`20` |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/2/tools/log_search/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "ad.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    advertiser_id = ADVERTISER_ID
    page = PAGE
    page_size = PAGE_SIZE
    
    # Args in JSON format
    my_args = "{\"advertiser_id\": \"%s\", \"page\": \"%s\", \"page_size\": \"%s\"}" % (advertiser_id, page, page_size)
    print(get(my_args))
```

## 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | object | json返回值 |
| ├─ page_info | json | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 页数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数。当前一次最多支持查询2000条日志数据，超过2000条返回的分页信息可能存在不准确的问题，建议您缩短查询时间（7天以内）。 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数，当前一次最多支持查询2000条日志数据，超过2000条返回的分页信息可能存在不准确的问题，建议您缩短查询时间（7天以内）。 |
| ├─ logs | object[] | 日志详情列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ content_title | string | 操作内容 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ object_type | string | 操作对象类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ object_id | int | 操作对象ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ object_name | string | 操作对象名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ create_time | string | 操作时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ content_log | string[] | 操作前后内容 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ operator | string | 操作人 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ opt_ip | string | 操作IP |
| request_id | string | 请求日志id |

## 响应示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "page_info": {
      "total_count": 50,
      "page": 1,
      "page_size": 20
    },
    "logs": [
      {
        "content_title": "修改",
        "object_type": "广告计划",
        "object_id": XXXXX,
        "object_name": "XXXXX",
        "create_time": "2019-07-19 15:11:04",
        "content_log": [
          "修改预算: 300.0 -> 420.0"
        ],
        "operator": "管理员",
        "opt_ip": ""
      }
    ]
  },
  "request_id": "XXXXX"
}
```

## 官方文档链接
[操作日志查询](https://open.oceanengine.com/labels/7/docs/1696710682956815?origin=left_nav)