# 巨量广告 - 获取视频素材

## 接口描述
通过此接口，用户可以获取经过一定条件过滤后的广告主下创意素材库的视频及视频信息。

## 重要提示
- 接口返回的预览链接仅限同主体进行素材预览查看
- 若非同主体会返回"素材所属主体与开发者主体不一致无法获取URL"
- 预览链接有效期为1小时，仅做预览使用

## 请求地址
```
GET https://api.oceanengine.com/open_api/2/file/video/get/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主ID |
| filtering | object | 可选 | 视频过滤条件 |
| └─ video_ids | string[] | 可选 | 视频id列表，可以根据video_ids（创意中使用的视频key，存在一张视频对应多个video_ids的情况）进行过滤<br/>数量限制：<=100<br/>注意：video_ids、material_ids、signatures只能选择一个进行过滤 |
| └─ material_ids | number[] | 可选 | 素材id列表，可以根据material_ids（素材报表使用的id，一个素材唯一对应一个素材id）进行过滤<br/>数量限制：<=100<br/>注意：video_ids、material_ids、signatures只能选择一个进行过滤 |
| └─ signatures | string[] | 可选 | md5值列表，可以根据素材的md5进行过滤<br/>数量限制：<=100<br/>注意：video_ids、material_ids、signatures只能选择一个进行过滤 |
| └─ width | double | 可选 | 视频宽度 |
| └─ height | double | 可选 | 视频高度 |
| └─ ratio | double[] | 可选 | 视频宽高比，eg: [1.7, 2.5]，输入1.7则搜索满足宽高比介于1.65-1.75之间的视频，即精度上下浮动0.05 |
| └─ start_time | string | 可选 | 根据视频上传时间进行过滤的起始时间，与end_time搭配使用，格式：yyyy-mm-dd |
| └─ end_time | string | 可选 | 根据视频上传时间进行过滤的截止时间，与start_time搭配使用，格式：yyyy-mm-dd |
| └─ labels | string[] | 可选 | 视频标签，eg: ["标签1", "标签2"] |
| └─ source | string[] | 可选 | 视频素材来源，可选值：<br/>• `AD_SITE` 本地上传<br/>• `BP` 组织共享<br/>• `ACCOUNT_PUSH` 账户推送<br/>• `AIC` 即创<br/>• `OPEN_API` MarketingAPI<br/>• `CEWEBRITY_VIDEO` 抖音主页 |
| └─ star_author_ids | number[] | 可选 | 达人账号id列表，数量限制：<=100 |
| page | number | 可选 | 页码，默认值1 |
| page_size | number | 可选 | 页面大小，默认值20，取值范围：1-100 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/2/file/video/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    advertiser_id = ADVERTISER_ID
    video_ids_list = VIDEO_IDS
    video_ids = json.dumps(video_ids_list)
    material_ids_list = MATERIAL_IDS
    material_ids = json.dumps(material_ids_list)
    signatures_list = SIGNATURES
    signatures = json.dumps(signatures_list)
    width = WIDTH
    height = HEIGHT
    ratio_list = RATIO
    ratio = json.dumps(ratio_list)
    start_time = START_TIME
    end_time = END_TIME
    labels_list = LABELS
    labels = json.dumps(labels_list)
    source_list = SOURCE
    source = json.dumps(source_list)
    star_author_ids_list = STAR_AUTHOR_IDS
    star_author_ids = json.dumps(star_author_ids_list)
    page = PAGE
    page_size = PAGE_SIZE

    # Args in JSON format
    my_args = "{\"advertiser_id\": \"%s\", \"filtering\": {\"video_ids\": %s, \"material_ids\": %s, \"signatures\": %s, \"width\": \"%s\", \"height\": \"%s\", \"ratio\": %s, \"start_time\": \"%s\", \"end_time\": \"%s\", \"labels\": %s, \"source\": %s, \"star_author_ids\": %s}, \"page\": \"%s\", \"page_size\": \"%s\"}" % (advertiser_id, video_ids, material_ids, signatures, width, height, ratio, start_time, end_time, labels, source, star_author_ids, page, page_size)
    print(get(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ list | object[] | 视频素材列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ id | string | 视频ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ material_id | number | 素材id，即多合一报表中的素材id，一个素材唯一对应一个素材id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ size | number | 视频大小，单位：字节 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ width | number | 视频宽度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ height | number | 视频高度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ url | string | 视频预览地址，仅限同主体进行素材预览查看，若非同主体会返回"素材所属主体与开发者主体不一致无法获取URL"，链接仅做预览使用，预览链接有效期为1小时 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ format | string | 视频格式，如：mp4 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ signature | string | 视频md5 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ poster_url | string | 视频封面预览地址，预览链接有效期为1小时 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ duration | number | 视频时长，单位：秒 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ bit_rate | number | 视频码率 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ source | string | 视频素材来源 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ create_time | string | 素材的上传时间，格式："yyyy-mm-dd HH:MM:SS" |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ filename | string | 素材的文件名 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ labels | string[] | 视频标签 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ star_author_id | number | 达人账号id |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ aigc | bool | 素材是否是aigc生成 |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ page | number | 当前页 |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "list": [
      {
        "id": "video123456789",
        "material_id": 987654321,
        "size": 2048576,
        "width": 1280,
        "height": 720,
        "url": "http://example.com/video/preview.mp4",
        "format": "mp4",
        "signature": "abc123def456ghi789",
        "poster_url": "http://example.com/video/poster.jpg",
        "duration": 30,
        "bit_rate": 1000,
        "source": "AD_SITE",
        "create_time": "2023-01-15 14:30:00",
        "filename": "promotional_video.mp4",
        "labels": ["商品推广", "品牌宣传"],
        "star_author_id": 123456,
        "aigc": false
      }
    ],
    "page_info": {
      "total_number": 50,
      "total_page": 3,
      "page_size": 20,
      "page": 1
    }
  },
  "request_id": "20230115143000123456"
}
```

## 官方文档链接
[获取视频素材](https://open.oceanengine.com/labels/7/docs/1696710601820172?origin=left_nav)