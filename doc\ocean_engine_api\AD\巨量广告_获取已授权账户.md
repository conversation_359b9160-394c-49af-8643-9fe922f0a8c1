# 巨量广告 - 获取已授权账户

## 接口描述
此接口用于获取AccessToken下已经授权的账户列表

## 重要提示
- 重新授权会覆盖前一次授权，需要确保此次授权已经勾选了想要的全部账户
- 账号与账户管理关系发生变更，同步会影响到AccessToken下授权账户关系

## 请求地址
```
GET https://api.oceanengine.com/open_api/oauth2/advertiser/get/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 根据授权auth_code获取生成的AccessToken |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| access_token | string | 必填 | 根据授权auth_code获取生成的AccessToken<br/>授权页面使用相同账号授权对应同一个AccessToken，如使用多个不同的账号授权，则需要区分维护多个不同的AccessToken |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/oauth2/advertiser/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    access_token = ACCESS_TOKEN

    # Args in JSON format
    my_args = "{\"access_token\": \"%s\"}" % (access_token)
    print(get(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ list | object[] | 账户信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_id | number | 账户ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_name | string | 账户名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ account_role | string | 账户类型，枚举值：<br/>• `ADVERTISER` 广告主<br/>• `CUSTOMER_ADMIN` 工作台-管理员<br/>• `CUSTOMER_OPERATOR` 工作台-协作者<br/>• `AGENT` 代理商<br/>• `CHILD_AGENT` 二级代理商<br/>• `PLATFORM_ROLE_STAR` 星图账户<br/>• `PLATFORM_ROLE_SHOP_ACCOUNT` 抖音店铺账户<br/>• `PLATFORM_ROLE_QIANCHUAN_AGENT` 千川代理商<br/>• `PLATFORM_ROLE_STAR_AGENT` 星图代理商<br/>• `PLATFORM_ROLE_AWEME` 抖音号<br/>• `PLATFORM_ROLE_STAR_MCN` 星图MCN机构<br/>• `PLATFORM_ROLE_STAR_ISV` 星图服务商<br/>• `AGENT_SYSTEM_ACCOUNT` 代理商系统账户<br/>• `PLATFORM_ROLE_LOCAL_AGENT` 本地推代理商<br/>• `PLATFORM_ROLE_YUNTU_BRAND_ISV_ADMIN` 云图品牌服务商管理员<br/>• `PLATFORM_ROLE_LIFE` 抖音来客账户<br/>• `PLATFORM_ROLE_ENTERPRISE_BP_ADMIN` 企业号-管理员<br/>• `PLATFORM_ROLE_ENTERPRISE_BP_OPERATOR` 企业号-协作者 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ is_valid | bool | 授权有效性<br/>**注意：** false则表示账号与账户已无管理关系，无权限继续操作使用，需在对应账户所属业务平台重新调整管理关系 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ company_list | object[] | 代理商账户下授权公司范围，仅当授权页面授权代理商账户时有效<br/>• company_list为空则代表当前代理商账户下所有adv均可访问<br/>• company_list不为空则代表仅能访问列表内部分客户下业务账户 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ customer_company_id | number | 客户公司ID |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ customer_company_name | string | 客户公司名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ account_string_id | string | 账户ID，仅当account_role=`PLATFORM_ROLE_AWEME`抖音号时有效，即为aweme_sec_uid，可用于Dou+接口调用 |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {}
}
```

## 官方文档链接
[获取已授权账户](https://open.oceanengine.com/labels/7/docs/****************?origin=left_nav)