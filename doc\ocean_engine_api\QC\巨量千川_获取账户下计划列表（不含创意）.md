# 获取账户下计划列表（不含创意）

## 接口描述
用于获取千川广告账户下创建的计划列表（不含创意部分）。默认可以拉取全部计划详情，可以通过过滤拉取部分计划。

## 请求地址
https://api.oceanengine.com/open_api/v1.0/qianchuan/ad/get/

## 请求方法
GET

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Access-Token | string | 必填，授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| advertiser_id | number | 必填，千川广告账户ID |
| request_aweme_info | number | 是否包含抖音号信息，允许值：<br>• 0：不包含<br>• 1：包含，默认不返回<br>注意：marketing_scene= SHOPPING_MALL 时，只允许传 0 |
| filtering | object | 必填，过滤条件 |
| └─ ids | number[] | 按计划ID过滤，list长度限制 1-100 |
| └─ ad_name | string | 按计划名称过滤，长度为1-30个字符 |
| └─ status | string | 按计划状态过滤，不传入即默认返回"所有不包含已删除"。详见【附录-广告计划查询状态】 |
| └─ campaign_scene | string[] | 按营销场景过滤，允许值：<br>• DAILY_SALE：日常销售（默认）<br>• NEW_CUSTOMER_TRANSFORMATION：新客转化<br>• LIVE_HEAT：直播间加热<br>• PLANT_GRASS：人群种草<br>• PRODUCT_HEAT：商品加热<br>• NEW_PRODUCT_BOOST：新品起量 |
| └─ marketing_goal | string | 必填，按营销目标过滤，允许值：<br>• VIDEO_PROM_GOODS：推商品<br>• LIVE_PROM_GOODS：推直播间 |
| └─ marketing_scene | string | 按广告类型过滤，默认为 FEED，允许值：<br>• ALL：全部<br>• FEED：通投广告<br>• SEARCH：搜索广告<br>• SHOPPING_MALL：商城广告 |
| └─ campaign_id | number | 按广告组ID过滤 |
| └─ ad_create_start_date | string | 计划创建开始时间，格式："yyyy-mm-dd" |
| └─ ad_create_end_date | string | 计划创建结束时间，与ad_create_start_date搭配使用，格式："yyyy-mm-dd"，时间跨度不能超过180天 |
| └─ ad_modify_time | string | 计划修改时间，精确到小时，格式："yyyy-mm-dd HH" |
| └─ aweme_id | number | 根据抖音号过滤 |
| └─ auto_manage_filter | string | 按是否为托管计划过滤，允许值：<br>• ALL：不限，默认为 ALL<br>• AUTO_MANAGE：托管计划<br>• NORMAL：非托管计划 |
| page | number | 页码，默认值：1 |
| page_size | number | 页面大小，允许值：10, 20, 50, 100, 200，默认值：10 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "YOUR_ACCESS_TOKEN"
PATH = "/qianchuan/ad/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    return urlunparse(("https", "ad.oceanengine.com/open_api/v1.0", path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == "__main__":
    # Args in JSON format
    my_args = "{\"advertiser_id\":\"ADVERTISER_ID\",\"filtering\":{\"ids\":\"IDS\",\"ad_name\":\"AD_NAME\",\"status\":\"STATUS\",\"marketing_goal\":\"MARKETING_GOAL\"},\"page\":\"PAGE\"\"page_size\":\"PAGE_SIZE\"}"
    print(get(my_args))
```

## 应答字段
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| └─ list | object[] | 计划列表 |
| └─ └─ ad_id | number | 计划ID |
| └─ └─ campaign_id | number | 广告组ID（若为托管计划，则返回null） |
| └─ └─ campaign_scene | string | 营销场景：<br>• DAILY_SALE：日常销售<br>• NEW_CUSTOMER_TRANSFORMATION：新客转化<br>• LIVE_HEAT：直播间加热<br>• PLANT_GRASS：人群种草<br>• PRODUCT_HEAT：商品加热<br>• NEW_PRODUCT_BOOST：新品起量 |
| └─ └─ marketing_goal | string | 营销目标 |
| └─ └─ marketing_scene | string | 广告类型：<br>• FEED：通投广告<br>• SEARCH：搜索广告<br>• SHOPPING_MALL：商城广告 |
| └─ └─ name | string | 计划名称 |
| └─ └─ status | string | 计划投放状态,详见【附录-枚举值】 |
| └─ └─ opt_status | string | 计划操作状态,详见【附录-枚举值】 |
| └─ └─ ad_create_time | string | 计划创建时间 |
| └─ └─ ad_modify_time | string | 计划更新时间 |
| └─ └─ lab_ad_type | string | 推广方式：<br>• NOT_LAB_AD：非托管计划<br>• LAB_AD：托管计划 |
| └─ └─ product_info | object[] | 商品列表 |
| └─ └─ └─ id | number | 商品id |
| └─ └─ └─ name | string | 商品名称 |
| └─ └─ └─ discount_price | float | 售价，已废弃 |
| └─ └─ └─ img | string | 商品主图 |
| └─ └─ └─ market_price | float | 原价，单位为元 |
| └─ └─ └─ discount_lower_price | float | 折扣价区间最小值，单位为元 |
| └─ └─ └─ discount_higher_price | float | 折扣价区间最大值，单位为元 |
| └─ └─ └─ channel_id | number | 渠道ID，如果渠道品生效，价格、销量等信息需要返回渠道品信息 |
| └─ └─ └─ channel_type | string | 渠道类型：<br>• STAR_SELL：达人自播<br>• SHOP_SELL：商家自卖 |
| └─ └─ aweme_info | object[] | 抖音号信息 |
| └─ └─ └─ aweme_id | number | 抖音ID |
| └─ └─ └─ aweme_name | string | 抖音昵称 |
| └─ └─ └─ aweme_show_id | string | 抖音号ID，抖音客户端展示ID |
| └─ └─ └─ aweme_avatar | string | 抖音号头像 |
| └─ └─ delivery_setting | object | 投放设置 |
| └─ └─ └─ deep_external_action | string | 深度转化目标，详见【附录-枚举值】 |
| └─ └─ └─ deep_bid_type | string | 深度出价方式：仅当深度转化目标"deep_external_action"为 AD_CONVERT_TYPE_LIVE_PAY_ROI 时有效。枚举值：MIN 等同于PC端，转化目标设置为"支付ROI" |
| └─ └─ └─ roi_goal | float | 支付ROI目标，最多支持两位小数，0.01～100 |
| └─ └─ └─ smart_bid_type | string | 投放场景 |
| └─ └─ └─ external_action | string | 转化目标 |
| └─ └─ └─ budget | float | 预算 |
| └─ └─ └─ revive_budget | float | 复活预算 |
| └─ └─ └─ budget_mode | string | 预算类型 |
| └─ └─ └─ cpa_bid | float | 转化出价 |
| └─ └─ └─ start_time | string | 投放开始时间 |
| └─ └─ └─ end_time | string | 投放结束时间 |
| └─ └─ └─ product_new_open | bool | 是否开启新品加速 |
| └─ └─ └─ qcpx_mode | string | 智能优惠券状态，是否开启：<br>• QCPX_MODE_OFF：不启用<br>• QCPX_MODE_ON：启用 |
| └─ └─ └─ allow_qcpx | bool | 是否支持智能优惠券：<br>• true：支持<br>• false：不支持 |
| └─ fail_list | number[] | 获取失败的计划id列表 |
| └─ page_info | object | 页面信息 |
| └─ └─ page | number | 页码 |
| └─ └─ page_size | number | 页面大小 |
| └─ └─ total_number | number | 总数 |
| └─ └─ total_page | number | 总页数 |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "request_id": "202105111551080102121931483A043E53",
  "data": {
    "page_info": {
      "total_number": 57,
      "page": 1,
      "total_page": 29,
      "page_size": 2
    },
    "list": [
      {
        "marketing_goal": "LIVE_PROM_GOODS",
        "opt_status": "ENABLE",
        "name": "2021-05-11_直播_15:47:40",
        "promotion_way": "SIMPLE",
        "status": "AUDIT",
        "external_action": "AD_CONVERT_TYPE_LIVE_CLICK_PRODUCT_ACTION",
        "product_info": [],
        "ad_create_time": "2021-05-11 15:49:01",
        "lab_ad_type": "LAB_AD",
        "delivery_setting": {
          "smart_bid_type": "SMART_BID_CUSTOM",
          "budget_mode": "BUDGET_MODE_TOTAL",
          "end_time": "2021-05-11",
          "start_time": "2021-05-11",
          "budget": 500.0,
          "cpa_bid": 1.0
        },
        "ad_id": 1699447403234343
      },
      {
        "marketing_goal": "LIVE_PROM_GOODS",
        "opt_status": "DISABLE",
        "name": "G组千川作业",
        "promotion_way": "STANDARD",
        "status": "AUDIT",
        "external_action": "AD_CONVERT_TYPE_LIVE_SUCCESSORDER_ACTION",
        "product_info": [],
        "ad_create_time": "2021-05-11 11:43:25",
        "lab_ad_type": "NOT_LAB_AD",
        "delivery_setting": {
          "smart_bid_type": "SMART_BID_CUSTOM",
          "budget_mode": "BUDGET_MODE_DAY",
          "end_time": "2021-05-31",
          "start_time": "2021-05-11",
          "budget": 800.0,
          "cpa_bid": 65.88
        },
        "ad_id": 1699431951714327
      }
    ]
  }
}
```

## 官方文档链接
https://open.oceanengine.com/labels/12/docs/1697467558690816?origin=left_nav