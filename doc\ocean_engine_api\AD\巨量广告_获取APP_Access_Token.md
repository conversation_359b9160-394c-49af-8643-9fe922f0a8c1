# 巨量广告 - 获取APP Access Token

## 接口描述
应用级token获取

## 请求地址
```
https://open.oceanengine.com/open_api/oauth2/app_access_token/
```

## 请求方法
**POST**

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Content-Type | string | 必填，请求消息类型，允许值：application/json |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| app_id | number | 必填，开发者申请的应用APP_ID，可通过"应用管理"界面查看 |
| secret | string | 必填，开发者应用的私钥Secret，可通过"应用管理"界面查看（确保填入secret与app_id对应以免报错！） |

## 请求示例
```python
def get_access_token():
    import requests
    open_api_url_prefix = "https://open.oceanengine.com/open_api/"
    uri = "oauth2/app_access_token/"
    url = open_api_url_prefix + uri
    data = {
        "app_id": 0,
        "secret": "xxx"
    }
    rsp = requests.post(url, json=data)
    rsp_data = rsp.json()
    return rsp_data
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ access_token | string | 应用级别token |
| ├─ expires_in | number | access_token剩余有效时间,单位(秒) |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "",
  "data": {
    "access_token": "",
    "expires_in": 86400
  }
}
```

## 官方文档链接
[获取APP Access Token](https://open.oceanengine.com/labels/7/docs/1713655428885516?origin=left_nav)