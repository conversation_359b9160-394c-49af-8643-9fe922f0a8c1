# 巨量千川 - 刷新Refresh Token

## 接口描述
由于Access_Token有效期（默认1天）较短,当Access_Token超时后，可以使用refresh_token进行刷新，每次刷新都会产生新的access_token和Refresh_Token，同时重置二者的有效期。

**重要说明：**
- Refresh_Token有效期是30天，但是刷新后会产生新的Refresh_token，老的Refresh_token会过期
- 为防止Token刷新后新token由于某些原因可能存储失败，导致无法使用问题，刷新后历史Token将保留10分钟的有效时间

**具体场景逻辑：**
- **场景一：Token被刷新** - 只要Token刷新成功获得了新的Access_Token和Refresh_Token，老的Access_Token和Refresh_Token将继续保留10分钟的有效期，10分钟后会自动过期
- **场景二：重新授权获得新的auth_code** - 历史auth_code和Token将立即失效
- **场景三：广告主取消授权** - APPID下与此广告主相关的auth_code和Token立即过期

## 请求地址
```
https://api.oceanengine.com/open_api/oauth2/refresh_token/
```

## 请求方法
**POST**

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Content-Type | string | 请求消息类型，允许值：application/json |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| app_id | number | 必填 | 开发者申请的应用APP_ID，可通过"应用管理"界面查看 |
| secret | string | 必填 | 开发者应用的私钥Secret，可通过"应用管理"界面查看（确保填入secret与app_id对应以免报错！） |
| grant_type | string | 必填 | 授权类型。允许值："refresh_token" |
| refresh_token | string | 必填 | 刷新token,从"获取Access Token"和"刷新Access Token"的返回结果中得到，刷新后会过期，请及时保存最新的token |

## 请求示例
```python
def refresh_access_token():
    import requests
    
    open_api_url_prefix = "https://api.oceanengine.com/open_api/"
    uri = "oauth2/refresh_token/"
    refresh_token_url = open_api_url_prefix + uri
    data = {
        "app_id": 0,
        "secret": "xxx",
        "grant_type": "refresh_token",
        "refresh_token": "xxx"
    }
    rsp = requests.post(refresh_token_url, json=data)
    rsp_data = rsp.json()
    return rsp_data
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| data.access_token | string | 用于验证权限的token |
| data.expires_in | number | access_token剩余有效时间,单位(秒) |
| data.refresh_token | string | 刷新access_token,用于获取新的access_token和refresh_token，并且刷新过期时间 |
| data.refresh_token_expires_in | number | refresh_token剩余有效时间,单位(秒) |
| data.request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "access_token": "26d6e22b1111111122222222333333334444444455555555abc",
    "expires_in": 86400,
    "refresh_token": "73af6e29bbbbbbbbccccccccddddddddeeeeeeeeffffffffaaaa",
    "refresh_token_expires_in": 2592000,
    "request_id": "20240101123456789"
  }
}
```

## 官方文档链接
[刷新Refresh Token](https://open.oceanengine.com/labels/12/docs/1697468248190020?origin=left_nav)