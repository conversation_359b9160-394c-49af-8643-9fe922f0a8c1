# 巨量千川 - 获取千川素材库图文

## 接口概述
获取千川素材库图文信息

## 请求信息

### 请求地址
```
GET https://api.oceanengine.com/open_api/v1.0/qianchuan/aweme/material_collection/image_text/
```

### 请求头
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Access-Token | string | 是 | 授权access_token，可以通过【获取Access token】接口获取 |

### 请求参数
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| advertiser_id | number | 是 | 广告主ID |
| aweme_id | number | 否 | 抖音号ID |
| page | number | 否 | 页码，默认值：1 |
| page_size | number | 否 | 页面大小，允许值：10, 20, 50, 100，默认值：10 |

### 请求示例
```python
# coding=utf-8
import json
import requests
from six.moves.urllib.parse import urlencode

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/aweme/material_collection/image_text/"

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode(args)
    url = f"https://api.oceanengine.com{PATH}?{query_string}"
    headers = {"Access-Token": ACCESS_TOKEN}
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    my_args = json.dumps({
        "advertiser_id": 123456789,
        "aweme_id": 987654321,
        "page": 1,
        "page_size": 10
    })
    print(get(my_args))
```

## 响应信息

### 响应参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码 |
| message | string | 返回信息 |
| data | object | 返回数据 |
| image_text_materials | object[] | 图文素材列表 |
| page_info | object | 分页信息 |
| request_id | string | 请求日志id |

### 响应示例
```json
{
    "code": 0,
    "message": "OK",
    "data": {
        "image_text_materials": [
            {
                "material_id": 123456789,
                "title": "图文标题",
                "description": "图文描述",
                "images": [
                    {
                        "image_url": "https://example.com/image1.jpg",
                        "width": 720,
                        "height": 1280
                    }
                ]
            }
        ],
        "page_info": {
            "page": 1,
            "page_size": 10,
            "total_page": 1,
            "total_num": 1
        },
        "request_id": "20240101123456789"
    }
}
```

## 官方文档链接
[https://open.oceanengine.com/labels/12/docs/1739309912219663](https://open.oceanengine.com/labels/12/docs/1739309912219663)