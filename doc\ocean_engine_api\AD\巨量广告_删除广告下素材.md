# 巨量广告 - 删除广告下素材

## 接口描述
**本接口支持删除一条广告下存在的素材，请注意：**
- 仅支持删除「巨量广告」账户下的广告素材
- 仅支持删除广告主在广告下添加的图片、视频、图文素材
- 不支持删除广告下「已删除」的素材，如传入的material_id在广告下的素材状态为已删除，调用本接口会报错"广告下不存在素材"
- 不支持将广告下素材全部删除，当广告下仅剩下1个素材时，再调用接口删除素材会报错

## 请求地址
```
POST https://api.oceanengine.com/open_api/v3.0/promotion/material/delete/
```

## 请求方法
**POST**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |
| Content-Type | string | 必填 | 请求消息类型，允许值：application/json |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主账户ID，仅支持巨量广告账户 |
| promotion_id | number | 必填 | 广告ID |
| material_id | number[] | 必填 | 素材ID，仅支持传入1个，必须是传入的广告id下存在的素材<br/>• 不支持删除状态为「已删除」的素材，此时删除会报错<br/>• 仅支持删除广告主在广告下添加的图片、视频、图文素材<br/>• 当广告下仅剩下1个素材时，再调用接口删除素材会报错 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/promotion/material/delete/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def post(json_str):
    # type: (str) -> dict
    """
    Send POST request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    url = build_url(PATH)
    args = json.loads(json_str)
    headers = {
        "Access-Token": ACCESS_TOKEN,
        "Content-Type": "application/json",
    }
    rsp = requests.post(url, headers=headers, json=args)
    return rsp.json()

if __name__ == '__main__':
    advertiser_id = ADVERTISER_ID
    promotion_id = PROMOTION_ID
    material_id_list = MATERIAL_ID
    material_id = json.dumps(material_id_list)
    
    # Args in JSON format
    my_args = "{\"advertiser_id\": \"%s\", \"promotion_id\": \"%s\", \"material_id\": %s}" % (advertiser_id, promotion_id, material_id)
    print(post(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {}
}
```

## 业务错误码
仅披露当前接口高频的参数校验报错，更多常见报错详见「返回码」文档

| Code | Message | 解决方案 |
|------|---------|----------|
| 400080 | 素材图片信息有误 | 请先检查图片素材的image_id是否正确，创建和编辑广告需要传入image_id而非material_id，相关信息可先从"获取广告列表"接口查询 |

## 官方文档链接
[删除广告下素材](https://open.oceanengine.com/labels/7/docs/1797183832412380?origin=left_nav)