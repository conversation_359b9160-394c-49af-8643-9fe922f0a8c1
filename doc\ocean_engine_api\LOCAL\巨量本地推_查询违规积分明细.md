# 巨量本地推 - 查询违规积分明细

## 接口描述
违规积分明细查询

## 请求地址
`GET https://api.oceanengine.com/open_api/v3.0/security/score_violation_event/get/`

## 请求方法
GET

## 请求Header

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主id |
| business_line | string | 必填 | 业务线，允许值：<br/>• `AD` - 广告<br/>• `LOCALAD` - 本地推 |
| page | number | 可选 | 页数，默认值：`1`，page范围为[1,99999] |
| page_size | number | 可选 | 页面大小，默认值：`10`，page_size范围为[1,100] |
| filtering | object | 可选 | 过滤器 |
| ├─ status | string | 可选 | 生效状态，允许值：<br/>• `APPEAL` - 已申诉（失效）<br/>• `FAILAPPEAL` - 申诉失败<br/>• `ONAPPEAL` - 申诉中<br/>• `VALID` - 生效<br/>• `TIMEOUT` - 已超时不可申诉 |
| ├─ start_time | string | 可选 | 筛选时间（开始），格式：yyyy-mm-dd HH:mm:ss |
| ├─ end_time | string | 可选 | 筛选时间（结束），格式：yyyy-mm-dd HH:mm:ss |
| ├─ event_id | number | 可选 | 积分违规单 |
| ├─ illegal_type | string | 可选 | 违规类型，可选值：<br/>• `GENERAL` - 一般违规（AD）<br/>• `SERIOUS` - 严重违规（AD）<br/>• `CRITICAL` - 严重违规（本地推）<br/>• `MINOR` - 一般违规（本地推） |

## 请求示例

```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/security/score_violation_event/get/"

def build_url(path, query=""):
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    advertiser_id = ADVERTISER_ID
    business_line = BUSINESS_LINE
    page = PAGE
    page_size = PAGE_SIZE
    status = STATUS
    start_time = START_TIME
    end_time = END_TIME
    event_id = EVENT_ID
    illegal_type = ILLEGAL_TYPE
    my_args = "{\"advertiser_id\": \"%s\", \"business_line\": \"%s\", \"page\": \"%s\", \"page_size\": \"%s\", \"filtering\": {\"status\": \"%s\", \"start_time\": \"%s\", \"end_time\": \"%s\", \"event_id\": \"%s\", \"illegal_type\": \"%s\"}}" % (advertiser_id, business_line, page, page_size, status, start_time, end_time, event_id, illegal_type)
    print(get(my_args))
```

## 响应参数

| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ adv_score_event | object[] | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ event_id | number | 违规单id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_id | number | 广告主id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ ad_id | number | 计划id（AD2.0为广告id，本地推为广告id） |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ material_id | string | 素材id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ violation_evidence_img | string | 违规证据截图 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ score | number | 扣罚分值 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ reject_reason | string | 拒绝理由 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ create_time | string | 创建时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ status | string | 状态，枚举值：<br/>• `APPEAL` - 已申诉（失效）<br/>• `FAILAPPEAL` - 申诉失败<br/>• `ONAPPEAL` - 申诉中<br/>• `VALID` - 生效 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ illegal_type | string | 违规类型，枚举值：<br/>• `GENERAL` - 一般违规（AD）<br/>• `SERIOUS` - 严重违规（AD） |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 页码 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| request_id | string | 请求日志id |

## 响应示例

```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "adv_score_event": [
      {
        "event_id": 123456789,
        "advertiser_id": 1234567890,
        "ad_id": 987654321,
        "material_id": "mat_123456",
        "violation_evidence_img": "https://example.com/evidence.jpg",
        "score": 10,
        "reject_reason": "广告内容与着陆页不符",
        "create_time": "2024-01-15 10:30:00",
        "status": "VALID",
        "illegal_type": "MINOR"
      }
    ],
    "page_info": {
      "page": 1,
      "page_size": 10,
      "total_page": 1,
      "total_number": 1
    }
  },
  "request_id": "20240712123456789abcdef"
}
```

## 官方文档链接
https://open.oceanengine.com/labels/37/docs/1807434338681868?origin=left_nav