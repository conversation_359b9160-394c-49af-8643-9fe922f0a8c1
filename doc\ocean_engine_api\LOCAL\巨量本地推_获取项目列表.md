# 巨量本地推 - 获取项目列表

## 接口描述
获取项目列表

**注意：该接口暂不支持拉取推广目的为获取线索的项目**

## 请求地址
`GET https://api.oceanengine.com/open_api/v3.0/local/project/list/`

## 请求方法
GET

## 请求Header

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| local_account_id | number | 必填 | 本地推广告账户ID |
| filtering | object | 可选 | 过滤字段 |
| ├─ project_ids | number[] | 可选 | 项目IDs筛选，最多100个 |
| ├─ project_status_first | string | 可选 | 项目一级状态筛选，允许值：<br/>• `PROJECT_STATUS_ALL` - 不限（包含已删除）<br/>• `PROJECT_STATUS_DELETE` - 已删除<br/>• `PROJECT_STATUS_DISABLE` - 未投放<br/>• `PROJECT_STATUS_DONE` - 已完成<br/>• `PROJECT_STATUS_ENABLE` - 启用中<br/>• `PROJECT_STATUS_NOT_DELETE` - 不限（不包含已删除）<br/>默认值：`PROJECT_STATUS_NOT_DELETE` |
| ├─ project_status_second | string | 可选 | 项目二级状态筛选，允许值：<br/>• `PROJECT_STATUS_BUDGET_EXCEED` - 项目超出预算<br/>• `PROJECT_STATUS_DISABLE` - 已暂停<br/>• `PROJECT_STATUS_NOT_SCHEDULE` - 不在投放时段<br/>• `PROJECT_STATUS_NOT_START` - 未达投放时间<br/>仅当status_first = `PROJECT_STATUS_DISABLE` 未投放时传入有效 |
| ├─ shop_ids | number[] | 可选 | 按门店IDs筛选，单次限制最多10个 |
| ├─ product_ids | number[] | 可选 | 按商品IDs筛选，单次限制最多10个 |
| ├─ local_delivery_scene | string | 可选 | 推广目的筛选，默认不限，允许值：<br/>• `ALL` - 不限<br/>• `CONTENT_HEAT` - 线上互动<br/>• `POI_RECOMMEND` - 线下到店<br/>• `PRODUCT_PAY` - 团购成交<br/>默认值：`ALL` |
| ├─ marketing_goal | string | 可选 | 营销场景筛选，默认不限，允许值：<br/>• `ALL` - 不限<br/>• `LIVE` - 直播<br/>• `VIDEO_IMAGE` - 短视频/图文<br/>默认值：`ALL` |
| ├─ ad_type | string | 可选 | 广告类型筛选，默认不限，允许值：<br/>• `ALL` - 不限<br/>• `GENERAL` - 通投广告<br/>• `SEARCHING` - 搜索广告<br/>默认值：`ALL` |
| ├─ project_name | string | 可选 | 项目名称，模糊搜索 |
| ├─ project_create_time_start | string | 可选 | 项目创建开始时间，格式 `yyyy-MM-dd HH:mm:ss`，与project_create_time_end搭配使用 |
| ├─ project_create_time_end | string | 可选 | 项目创建结束时间，格式 `yyyy-MM-dd HH:mm:ss`，与project_create_time_start搭配使用 |
| ├─ project_modify_time_start | string | 可选 | 项目更新开始时间，格式 `yyyy-MM-dd HH:mm:ss`，与project_modify_time_end搭配使用 |
| ├─ project_modify_time_end | string | 可选 | 项目更新结束时间，格式 `yyyy-MM-dd HH:mm:ss`，与project_modify_time_start搭配使用 |
| ├─ bid_type | string | 可选 | 出价方式，默认不限，允许值：<br/>• `ALL` - 不限<br/>• `MANUAL` - 手动出价<br/>• `SMART` - 智能出价<br/>默认值：`ALL` |
| page | number | 可选 | 页码，默认值 `1` |
| page_size | number | 可选 | 页面大小，最大值 `100`，默认值 `20` |

## 请求示例

```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/local/project/list/"

def build_url(path, query=""):
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    local_account_id = *********  # 替换为实际的本地推广告账户ID
    my_args = "{\"local_account_id\": \"%s\", \"filtering\": {\"project_ids\": [], \"project_status_first\": \"PROJECT_STATUS_NOT_DELETE\", \"local_delivery_scene\": \"ALL\", \"marketing_goal\": \"ALL\", \"ad_type\": \"ALL\"}, \"page\": \"1\", \"page_size\": \"20\"}" % (local_account_id)
    print(get(my_args))
```

## 响应参数

| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | 返回数据 |
| ├─ project_list | object[] | 项目列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ project_id | number | 项目ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ project_status_first | string | 项目一级状态 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ project_status_second | string[] | 项目二级状态 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ local_account_id | number | 本地推广告账户ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ ad_type | string | 广告类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ local_delivery_scene | string | 推广目的 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ marketing_goal | string | 营销场景 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ name | string | 项目名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ project_create_time | string | 项目创建时间，格式：`yyyy-MM-dd HH:mm:ss` |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ project_modify_time | string | 项目更新时间，格式：`yyyy-MM-dd HH:mm:ss` |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ external_action | string | 优化目标，枚举值：<br/>• `FOLLOW_ACTION` - 粉丝增长<br/>• `LIVE_ENGAGEMENT` - 直播加热<br/>• `LIVE_ENTER_ACTION` - 直播间观看<br/>• `LIVE_OTO_CLICK` - 直播间商品点击<br/>• `LIVE_OTO_GROUP_BUYING` - 直播间团购购买<br/>• `LIVE_STAY_TIME` - 直播间停留<br/>• `NATIVE_ACTION` - 用户互动<br/>• `SHOW` - 展示量<br/>• `OTO_PAY` - 团购购买<br/>• `POI_RECOMMEND` - 门店浏览 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ delivery_goal | string | 投放内容，枚举值：<br/>• `POI` - 门店<br/>• `PRODUCT` - 商品 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ project_budget_mode | string | 项目预算类型，枚举值：<br/>• `BUDGET_MODE_DAY` - 日预算<br/>• `BUDGET_MODE_TOTAL` - 总预算 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ project_budget | string | 项目预算，单位为：分 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ bid_type | string | 出价方式，枚举值：<br/>• `MANUAL` - 手动出价<br/>• `SMART` - 智能出价 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ project_bid | string | 项目出价，单位为：分 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ start_time | string | 投放开始时间，如：2017-01-01 精确到天 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ end_time | string | 投放结束时间，如：2017-01-01 精确到天 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ poi_info | object | 项目推广门店信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ poi_id | number | 门店id（仅推广单门店项目返回，推广多门店时不返回） |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ poi_name | string | 门店名称（仅推广单门店项目返回，推广多门店时不返回） |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ poi_image_url | string | 门店头图（仅推广单门店项目返回，推广多门店时不返回） |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ multi_poi_num | number | 推广门店数量 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ all_poi_mode | string | 推广门店类型，枚举值：<br/>• `ALL` - 全部门店<br/>• `PART` - 指定门店 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ auto_update_pois | string | 是否自动更新门店 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ product_info | object | 商品信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ product_id | number | 商品id |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ product_name | string | 商品名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├─ product_image_url | string | 商品头图 |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 页码 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| request_id | string | 请求日志id |

## 响应示例

```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "project_list": [
      {
        "project_id": ****************,
        "project_status_first": "PROJECT_STATUS_ENABLE",
        "project_status_second": [],
        "local_account_id": *********,
        "ad_type": "GENERAL",
        "local_delivery_scene": "PRODUCT_PAY",
        "marketing_goal": "VIDEO_IMAGE",
        "name": "示例团购商品推广项目",
        "project_create_time": "2023-06-01 10:00:00",
        "project_modify_time": "2023-06-15 14:30:00",
        "external_action": "OTO_PAY",
        "delivery_goal": "PRODUCT",
        "project_budget_mode": "BUDGET_MODE_DAY",
        "project_budget": "50000",
        "bid_type": "SMART",
        "project_bid": "1000",
        "start_time": "2023-06-01",
        "end_time": "2023-12-31",
        "poi_info": {
          "poi_id": 7001,
          "poi_name": "示例餐厅",
          "poi_image_url": "https://example.com/poi_image.jpg",
          "multi_poi_num": 1,
          "all_poi_mode": "PART",
          "auto_update_pois": "false"
        },
        "product_info": {
          "product_id": 8001,
          "product_name": "精选套餐",
          "product_image_url": "https://example.com/product_image.jpg"
        }
      },
      {
        "project_id": ****************,
        "project_status_first": "PROJECT_STATUS_DISABLE",
        "project_status_second": ["PROJECT_STATUS_DISABLE"],
        "local_account_id": *********,
        "ad_type": "SEARCHING",
        "local_delivery_scene": "POI_RECOMMEND",
        "marketing_goal": "LIVE",
        "name": "示例门店推广项目",
        "project_create_time": "2023-06-02 09:30:00",
        "project_modify_time": "2023-06-16 11:20:00",
        "external_action": "POI_RECOMMEND",
        "delivery_goal": "POI",
        "project_budget_mode": "BUDGET_MODE_TOTAL",
        "project_budget": "100000",
        "bid_type": "MANUAL",
        "project_bid": "800",
        "start_time": "2023-06-02",
        "end_time": "2023-11-30",
        "poi_info": {
          "multi_poi_num": 5,
          "all_poi_mode": "PART",
          "auto_update_pois": "true"
        },
        "product_info": null
      }
    ],
    "page_info": {
      "page": 1,
      "page_size": 20,
      "total_number": 50,
      "total_page": 3
    }
  },
  "request_id": "20240101*********abcdef"
}
```

## 官方文档链接
https://open.oceanengine.com/labels/37/docs/1807977310878736?origin=left_nav