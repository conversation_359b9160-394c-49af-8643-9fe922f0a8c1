# 巨量广告API - 返回码

## 概述
本文档列出了巨量广告API调用过程中可能返回的各种状态码及其含义。正确理解和处理这些返回码对于API的稳定使用至关重要。

**重要说明**: 本文档内容完全基于巨量广告官方文档，确保信息的准确性和时效性。

## 返回码格式
所有API接口返回的数据都遵循统一的JSON格式：

```json
{
  "code": 0,
  "message": "OK",
  "data": {
    // 具体的返回数据
  },
  "request_id": "202312345678901234567890"
}
```

## 成功返回

### 成功状态码
| code | msg | 排查建议 |
|------|-----|----------|
| 0 | OK | 请求成功 |

## 通用返回

**说明**: 4开头错误码均为正常业务校验报错，如参数类型不符合要求，或不符合业务要求。

### 40000系列 - 参数校验错误
| code | msg | 排查建议 |
|------|-----|----------|
| 40000 | The advertiser ID or the account information is invalid. Please check and retry. | 传入advertiser_id错误，或不符合接口要求类型，注意查看接口要求传入的账户类型为广告主还是其他类型 |
| 40000 | xx : Missing data for required field. | xx为必填字段，请注意查看接口文档字段描述要求 |
| 40000 | xx: value is required but missing | 未传入必填字段xx，请注意查看接口文档字段描述要求 |
| 40000 | xx: Value must be an yy | 传入字段xx不符合要求类型yy，注意查看接口文档字段描述要求 |
| 40000 | xx: Field must be set to yy | 传入字段xx不符合要求类型yy，注意查看接口文档字段描述要求 |
| 40000 | xx: Value is not nullable | xx字段内容不能为空，如非必填字段，不使用不要传入字段 |
| 40000 | The advertiser ID is required. Please provide it and retry. | 未传入必填advertiser_id，注意检查传入参数，以及请求格式是否符合要求，get请求以query形式传入，post请求以body形式传入 |

### 40002系列 - 权限错误
| code | msg | 排查建议 |
|------|-----|----------|
| 40002 | No permission to operate account xxx | 使用Access_Token下无对应xxx账户授权，注意通过【获取已授权账号】接口查询确认Access_Token可操作账户 |
| 40002 | advertiser does no grant xxx permission | 在申请授权时未向广告主申请对应接口xxx授权，则无法操作该广告主相关接口能力，可重新申请广告主授权对应功能 |
| 40002 | app_secret不匹配，无权限操作当前应用 | 传入secret与app_id不一致，请登陆开发者后台查询应用信息确认 |

### 40100系列 - 请求频控错误
| code | msg | 排查建议 |
|------|-----|----------|
| 40100 | Too many requests. Please retry in some time. | 请求超过服务整体频控，可以在开发者频控范围内重新尝试请求 |
| 40110 | Too many requests by this developer. Please retry in some time. | 请求超过开发者频控范围，请登陆开发者后台-接口频控处查看当前接口对应分配qps，在要求范围内请求接口 |

### Token相关错误
| code | msg | 排查建议 |
|------|-----|----------|
| 40102 | access_token已过期 | 传入Access_Token已失效，请重新获取 |
| 40103 | refresh_token已过期 | 传入refresh_token已失效，失效原因一般是由于refresh_token已被使用，或授权账号重新授权并生成了新的Token |
| 40104 | The access_token is empty. | 传入Access_Token无效，请重新获取正确Access_Token传入 |
| 40107 | refresh_token无效，请传入最新的refresh_token | 传入refresh_token已失效，失效原因一般是由于refresh_token已被使用，或授权账号重新授权并生成了新的Token |

### 应用相关错误
| code | msg | 排查建议 |
|------|-----|----------|
| 40113 | App不存在或者状态异常 | Access_Token对应的开发者应用状态异常，请登陆开发者后台查看确认应用是否存在以及状态是否启用 |
| 40115 | 授权码无效 | auth_code已失效，失效原因：1. 已使用 2. 已过10分钟有效期 3. 同账号重新授权生成 |
| 40115 | auth_code已经被使用，请重新授权 | auth_code已失效，失效原因：1. 已使用 2. 已过10分钟有效期 3. 同账号重新授权生成 |
| 40118 | The API is not in allow list. Please apply for the allow list and retry. | 接口未上线，Access_Token对应开发者无对应接口请求权限 |
| 40119 | The developer and the advertiser do not belong to the same group. Please contact the administrator. | 线索、评论这类相对敏感的物料接口存在主体校验，如果开发者主体和操作的广告主主体不一致，接口会报错提示，如需操作需在申请授权时同时申请敏感物料授权，即授权链接中拼接参数material_auth=1 |

## 系统相关

### 50000系列 - 系统错误
| code | msg | 排查建议 |
|------|-----|----------|
| 50000 | SYS_ERROR | 系统请求超时，可以重试 |

## 错误处理最佳实践

### 1. 重试机制
- **系统错误(50000)**：建议实施指数退避重试策略
- **频控错误(40100, 40110)**：等待一段时间后重试
- **Token过期(40102, 40103)**：刷新Token后重试
- 最大重试次数不超过3次

### 2. 参数校验处理
- **40000系列错误**：仔细检查接口文档要求
  - 确认必填字段是否完整
  - 验证字段类型是否正确
  - 检查参数值是否符合规范
  - 注意GET请求使用query参数，POST请求使用body参数

### 3. 权限问题处理
- **40002系列错误**：
  - 通过【获取已授权账号】接口确认授权状态
  - 检查应用是否申请了对应接口权限
  - 验证app_secret与app_id的匹配性
  - 确认开发者主体与广告主主体关系

### 4. 授权码处理
- **40115错误**：
  - auth_code只能使用一次
  - auth_code有效期为10分钟
  - 同账号重新授权会使旧的auth_code失效

### 5. 错误日志记录
- 记录完整的请求和响应信息
- 包含request_id用于问题追踪
- 记录时间戳和错误上下文
- 记录具体的错误码和错误信息

### 6. 用户友好提示
- 将技术错误码转换为用户友好的提示信息
- 根据错误类型提供具体的解决方案
- 对于权限类错误，指导用户进行重新授权

### 7. 监控和告警
- 监控API调用成功率
- 设置关键错误码的告警阈值
- 特别关注40100/40110频控错误的发生频率
- 监控Token过期情况，及时刷新

## 常见问题排查流程

### Access Token相关问题
1. **检查Token格式**：确认Token格式正确
2. **验证Token有效性**：检查是否过期(40102)
3. **确认授权范围**：验证是否有操作对应账户的权限(40002)
4. **检查应用状态**：确认应用是否正常启用(40113)

### 参数错误问题
1. **检查必填参数**：确认所有必填字段都已提供
2. **验证参数类型**：检查参数类型是否符合接口要求
3. **确认参数值**：验证参数值是否在允许范围内
4. **检查请求格式**：GET用query，POST用body

### 权限问题
1. **验证账户授权**：通过获取已授权账号接口确认
2. **检查接口权限**：确认是否申请了对应接口的使用权限
3. **确认主体关系**：检查开发者与广告主的主体关系
4. **敏感物料授权**：涉及线索、评论等需要特殊授权

### 频控问题
1. **检查QPS限制**：登录开发者后台查看接口频控配置
2. **优化请求频率**：合理控制API调用频率
3. **实施重试策略**：频控错误后等待适当时间重试

## 官方文档链接
[巨量广告API返回码官方文档](https://open.oceanengine.com/labels/7/docs/1696710760866831?origin=left_nav)

## 更新记录
- **2025-07-04**: 基于官方文档重新生成，确保返回码信息的准确性
- 移除了推测性的错误码，仅保留官方文档中明确定义的返回码
- 补充了详细的排查建议和处理流程