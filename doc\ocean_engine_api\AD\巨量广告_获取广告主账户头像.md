# 巨量广告 - 获取广告主账户头像

## 接口描述
获取广告主账户头像信息。

## 请求地址
```
GET https://ad.oceanengine.com/open_api/2/advertiser/avatar/get/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主ID |

## 请求示例
```python
def video_cover_suggest():
    import requests
    open_api_url_prefix = "https://ad.oceanengine.com/open_api/"
    uri = "2/advertiser/avatar/get/"
    url = open_api_url_prefix + uri
    params = {
        "advertiser_id": 0
    }
    headers = {"Access-Token": "xxx"}
    rsp = requests.get(url, params=params, headers=headers)
    rsp_data = rsp.json()
    return rsp_data
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ advertiser_id | string | 广告主id |
| ├─ avatar_status | number | 头像审核状态<br/>• `0` UNSET -未设置<br/>• `1` IN_AUDIT -审核中<br/>• `2` AUDIT_REJECT -审核被拒<br/>• `3` AUDIT_PASS -审核通过 |
| ├─ avatar_reason | string | 头像被拒原因 |
| ├─ source_status | number | 品牌审核状态<br/>• `0` UNSET -未设置<br/>• `1` IN_AUDIT -审核中<br/>• `2` AUDIT_REJECT -审核被拒<br/>• `3` AUDIT_PASS -审核通过 |
| ├─ source_info | string | 品牌信息 |
| ├─ source_reason | string | 品牌信息被拒原因 |
| ├─ avatar_info | json | 头像信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ web_uri | string | 当前头像的uri |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ audit_web_uri | string | 审核中头像的uri |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ height | number | 审核中头像的高度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ width | number | 审核中头像的宽度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ web_url | string | 当前头像的预览链接，有效期1h |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ audit_web_url | string | 审核中头像的预览链接，有效期1h |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "advertiser_id": 123123123,
    "avatar_status": 1, 
    "avatar_reason": "xxxxxxx",
    "source_status": 1, 
    "source_info": "xxxxxx",
    "source_reason": "xxxxxsx",
    "avatar_info": {
      "web_uri": "web.business.image/2018850fa1ba94912a2f2",
      "audit_web_uri": "web.business.image/21213120fa1ba94912a2f2", 
      "web_url": "http://p3-mapi-sign.creativityeco.com/web.business.image/2018850fa1ba94912a2f2",
      "audit_web_url": "http://p3-mapi-sign.creativityeco.com/web.business.image/2018850fa1ba94912a2f2", 
      "height": 200, 
      "width": 200
    }
  }
}
```

## 官方文档链接
[获取广告主账户头像](https://open.oceanengine.com/labels/7/docs/1696710512904192?origin=left_nav)