# 巨量本地推 - 获取广告列表

## 接口描述
获取广告列表

**该接口暂不支持拉取推广目的为获取线索的项目下的广告**

## 请求地址
`GET https://api.oceanengine.com/open_api/v3.0/local/promotion/list/`

## 请求方法
GET

## 请求Header

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| local_account_id | number | 必填 | 本地推广告账户ID |
| filtering | object | 可选 | 筛选项 |
| ├─ promotion_ids | number[] | 可选 | 按广告IDs筛选，单次最多100个 |
| ├─ promotion_name | string | 可选 | 按广告名称模糊搜索 |
| ├─ project_id | number | 可选 | 项目ID筛选 |
| ├─ promotion_status_first | string | 可选 | 广告一级状态过滤，默认不限（不包含已删除），允许值：<br/>• `PROMOTION_STATUS_ALL` - 不限（包含已删除）<br/>• `PROMOTION_STATUS_DELETED` - 已删除<br/>• `PROMOTION_STATUS_DISABLE` - 未投放<br/>• `PROMOTION_STATUS_DONE` - 已完成<br/>• `PROMOTION_STATUS_ENABLE` - 投放中<br/>• `PROMOTION_STATUS_FROZEN` - 已终止<br/>• `PROMOTION_STATUS_NOT_DELETE` - 不限（不包含已删除）<br/>默认值：`PROMOTION_STATUS_NOT_DELETE` |
| ├─ promotion_status_second | string | 条件必填 | 广告二级状态过滤，允许值：<br/>仅当promotion_status_first=`PROMOTION_STATUS_DISABLE` 未投放时传入有效且必填，不传会报错；其他情况下传入该字段无效。<br/>• `AUDIT_DENY` - 审核不通过<br/>• `AUDIT` - 新建审核中<br/>• `REAUDIT` - 修改审核中<br/>• `DISABLED` - 已暂停<br/>• `DISABLE_BY_QUOTA` - 配额达限<br/>• `PROJECT_DISABLED` - 项目已被暂停<br/>• `NO_SCHEDULE` - 不在投放时段<br/>• `TIME_NO_REACH` - 未到达投放时间<br/>• `OFFLINE_BALANCE` - 账户余额不足<br/>• `BALANCE_OFFLINE_BUDGET` - 账户超出预算<br/>• `PROJECT_OFFLINE_BUDGET` - 项目超出预算<br/>• `PROMOTION_OFFLINE_BUDGET` - 广告超出预算<br/>• `LIVE_ROOM_OFF` - 直播间不可投放<br/>• `AWEME_ACCOUNT_DISABLED` - 抖音账号不可投放<br/>• `PRODUCT_OR_POI_OFFLINE` - 商品/门店不可投 |
| ├─ ad_type | string | 可选 | 广告类型筛选，默认不限，允许值：<br/>• `ALL` - 不限<br/>• `GENERAL` - 通投广告<br/>• `SEARCHING` - 搜索广告<br/>默认值：`ALL` |
| ├─ marketing_goal | string | 可选 | 营销场景筛选，默认不限，允许值：<br/>• `ALL` - 不限<br/>• `LIVE` - 直播<br/>• `VIDEO_IMAGE` - 短视频/图文<br/>默认值：`ALL` |
| ├─ promotion_create_time_start | string | 可选 | 广告创建开始时间，格式 `yyyy-MM-dd HH:mm:ss` 与time_end搭配使用 |
| ├─ promotion_create_time_end | string | 可选 | 广告创建结束时间，格式 `yyyy-MM-dd HH:mm:ss` 与time_start搭配使用 |
| ├─ promotion_modify_time_start | string | 可选 | 广告更新开始时间，格式 `yyyy-MM-dd HH:mm:ss` 与time_end搭配使用 |
| ├─ promotion_modify_time_end | string | 可选 | 广告更新结束时间，格式 `yyyy-MM-dd HH:mm:ss` 与time_start搭配使用 |
| ├─ reject_reason_type | string | 可选 | 审核建议类型筛选，默认不限，允许值：<br/>• `ALL` - 不限<br/>• `NONE` - 无建议<br/>• `LOW_MATERAIL` - 低俗素材<br/>• `QUALITY_POOR` - 素材质量低<br/>• `EXAGGERATION` - 夸大宣传<br/>• `ELSE` - 其他<br/>• `DISCOMFORT` - 引人不适<br/>• `REVIEW_REJECT` - 审核不通过<br/>默认值：`ALL` |
| ├─ learning_phase | string | 可选 | 学习期状态筛选，默认不限，允许值：<br/>• `ALL` - 不限<br/>• `LEARNED` - 学习期结束<br/>• `LEARNING` - 学习中<br/>• `LEARN_FAILED` - 学习失败<br/>默认值：`ALL` |
| ├─ budget_mode | string | 可选 | 预算类型筛选，默认不限，允许值：<br/>• `ALL` - 不限<br/>• `BUDGET_MODE_DAY` - 日预算<br/>• `BUDGET_MODE_TOTAL` - 总预算<br/>默认值：`ALL` |
| ├─ bid_type | string | 可选 | 出价方式筛选，默认不限，允许值：<br/>• `ALL` - 不限<br/>• `MANUAL` - 手动出价<br/>• `SMART` - 智能出价<br/>默认值：`ALL` |
| page | number | 可选 | 页码，默认值 `1` |
| page_size | number | 可选 | 页面大小，最大值 `100`，默认 `10` |

## 请求示例

```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/local/promotion/list/"

def build_url(path, query=""):
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    local_account_id = *********  # 替换为实际的本地推广告账户ID
    my_args = "{\"local_account_id\": \"%s\", \"filtering\": {\"promotion_status_first\": \"PROMOTION_STATUS_NOT_DELETE\", \"ad_type\": \"ALL\", \"marketing_goal\": \"ALL\"}, \"page\": \"1\", \"page_size\": \"10\"}" % (local_account_id)
    print(get(my_args))
```

## 响应参数

| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | 返回数据 |
| ├─ promotion_list | object[] | 广告列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ project_id | number | 项目ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ local_account_id | number | 广告账户id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ ad_type | string | 广告类型，枚举值：<br/>• `GENERAL` - 通投广告<br/>• `SEARCHING` - 搜索广告 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_id | number | 广告ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_name | string | 广告名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_create_time | string | 广告创建时间，格式 `yyyy-MM-dd HH:mm:ss` |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_modify_time | string | 广告更新时间，格式 `yyyy-MM-dd HH:mm:ss` |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_status_first | string | 广告一级状态 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ promotion_status_second | string[] | 广告二级状态 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ learning_phase | string | 学习期状态，枚举值：<br/>• `LEARNED` - 学习期结束<br/>• `LEARNING` - 学习中<br/>• `LEARN_FAILED` - 学习失败 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ aweme_id | string | 抖音号 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ aweme_name | string | 抖音号昵称 |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 页码 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| request_id | string | 请求日志id |

## 响应示例

```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "promotion_list": [
      {
        "project_id": ****************,
        "local_account_id": *********,
        "ad_type": "GENERAL",
        "promotion_id": ***************,
        "promotion_name": "示例通投广告",
        "promotion_create_time": "2023-06-01 10:30:00",
        "promotion_modify_time": "2023-06-15 14:20:30",
        "promotion_status_first": "PROMOTION_STATUS_ENABLE",
        "promotion_status_second": [],
        "learning_phase": "LEARNED",
        "aweme_id": "abc123456",
        "aweme_name": "示例抖音账号"
      },
      {
        "project_id": ****************,
        "local_account_id": *********,
        "ad_type": "SEARCHING",
        "promotion_id": ***************,
        "promotion_name": "示例搜索广告",
        "promotion_create_time": "2023-06-02 09:15:00",
        "promotion_modify_time": "2023-06-16 11:45:20",
        "promotion_status_first": "PROMOTION_STATUS_DISABLE",
        "promotion_status_second": ["DISABLED"],
        "learning_phase": "LEARNING",
        "aweme_id": "def789012",
        "aweme_name": "另一个抖音账号"
      }
    ],
    "page_info": {
      "page": 1,
      "page_size": 10,
      "total_number": 25,
      "total_page": 3
    }
  },
  "request_id": "20240101*********abcdef"
}
```

## 官方文档链接
https://open.oceanengine.com/labels/37/docs/****************?origin=left_nav