# 巨量千川 - 获取千川广告账户全量信息

## 接口描述
获取广告主账户详细信息。

## 请求地址
```
https://api.oceanengine.com/open_api/2/advertiser/info/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access-token，获取方法见接口文档【获取Access-Token】 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_ids | number[] | 必填 | 广告主ID集合(如果包含没有访问权限的ID,将返回no permission error)，取值范围: 1-100 |
| fields | string[] | 选填 | 查询字段集合, 默认:查询所有。字段详见下方response字段定义。允许值: "id", "name", "role", "status", "address", "reason", "license_url", "license_no", "license_province", "license_city", "company", "brand", "promotion_area", "promotion_center_province", "promotion_center_city", "industry", "create_time" |

## 请求示例
```python
def get_advertiser_info():
    import requests
    
    open_api_url_prefix = "https://api.oceanengine.com/open_api/"
    uri = "2/advertiser/info/"
    url = open_api_url_prefix + uri
    params = {
        "advertiser_ids": [0],
        "fields": ["id", "name", "status"]
    }
    headers = {"Access-Token": "xxx"}
    rsp = requests.get(url, json=params, headers=headers)
    rsp_data = rsp.json()
    return rsp_data
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | object[] | 广告主列表 |
| data[].id | number | 广告主ID |
| data[].name | string | 账户名 |
| data[].role | string | 角色, 详见【附录-广告主角色】 |
| data[].status | string | 状态,详见【附录-广告主状态】 |
| data[].address | string | 地址 |
| data[].license_url | string | 执照预览地址(链接默认1小时内有效) |
| data[].license_no | string | 执照编号 |
| data[].license_province | string | 执照省份 |
| data[].license_city | string | 执照城市 |
| data[].company | string | 公司名 |
| data[].brand | string | 经营类别 |
| data[].promotion_area | string | 运营区域 |
| data[].promotion_center_province | string | 运营省份 |
| data[].promotion_center_city | string | 运营城市 |
| data[].first_industry_name | string | 一级行业名称（新版） |
| data[].second_industry_name | string | 二级行业名称（新版） |
| data[].reason | string | 审核拒绝原因 |
| data[].create_time | string | 创建时间 |
| request_id | string | 请求接口日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "data": [
    {
      "id": *********,
      "name": "千川广告账户",
      "role": 6,
      "status": "STATUS_ENABLE",
      "address": "北京市朝阳区",
      "reason": "",
      "license_url": "https://example.com/license.jpg",
      "license_no": "91110000000000000X",
      "license_province": "北京市",
      "license_city": "北京市",
      "company": "北京科技有限公司",
      "brand": "品牌名称",
      "promotion_area": "全国",
      "promotion_center_province": "北京市",
      "promotion_center_city": "北京市",
      "industry": "电子商务",
      "create_time": "2024-01-01 09:00:00"
    }
  ],
  "request_id": "20240101*********"
}
```

## 官方文档链接
[获取千川广告账户全量信息](https://open.oceanengine.com/labels/12/docs/1697468139129870?origin=left_nav)