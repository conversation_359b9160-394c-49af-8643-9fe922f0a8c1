# 巨量千川 - 返回码

## 接口描述
本文档提供巨量千川开放平台API接口的返回码信息，包括成功返回码、通用返回码、系统相关返回码和限制相关返回码。

## 成功返回

| code | msg | 排查建议 |
|------|-----|----------|
| 0 | OK | 请求成功 |

## 通用返回

4开头错误码均为正常业务校验报错，如参数类型不符合要求，或不符合业务要求

| code | msg | 排查建议 |
|------|-----|----------|
| 40000 | The advertiser ID or the account information is invalid. Please check and retry. | 传入advertiser_id错误，或不符合接口要求类型，注意查看接口要求传入的账户类型为广告主还是其他类型 |
| 40000 | xx : Missing data for required field. | xx 为必填字段，请注意查看接口文档字段描述要求 |
| 40000 | xx: value is required but missing | 未传入必填字段 xx ，请注意查看接口文档字段描述要求 |
| 40000 | xx: Value must be an yy | 传入字段 xx 不符合要求类型 yy ，注意查看接口文档字段描述要求 |
| 40000 | xx: Field must be set to yy | 传入字段 xx 不符合要求类型 yy ，注意查看接口文档字段描述要求 |
| 40000 | xx: Value is not nullable | xx 字段内容不能为空，如非必填字段，不使用不要传入字段 |
| 40000 | The advertiser ID is required. Please provide it and retry. | 未传入必填advertiser_id，注意检查传入参数，以及请求格式是否符合要求，get请求以query形式传入，post请求以body形式传入 |
| 40002 | No permission to operate account xxx | 使用Access_Token下无对应 xxx 账户授权，注意通过【获取已授权账号】接口查询确认Access_Token可操作账户 |
| 40002 | advertiser does no grant xxx permission | 在申请授权时未向广告主申请对应接口 xxx 授权，则无法操作该广告主相关接口能力，可重新申请广告主授权对应功能 |
| 40002 | app_secret 不匹配，无权限操作当前应用 | 传入secret与app_id不一致，请登陆开发者后台查询应用信息确认 |
| 40100 | Too many requests. Please retry in some time. | 请求超过服务整体频控，可以在开发者频控范围内重新尝试请求 |
| 40110 | Too many requests by this developer. Please retry in some time. | 请求超过开发者频控范围，请登陆开发者后台-接口频控处查看当前接口对应分配qps，在要求范围内请求接口 |
| 40102 | access_token已过期 | 传入Access_Token已失效，请重新获取 |
| 40103 | refresh_token已过期 | 传入refresh_token已失效，失效原因一般是由于refresh_token已被使用，或授权账号重新授权并生成了新的Token |
| 40104 | The access_token is empty. | 传入Access_Token无效，请重新获取正确Access_Token传入 |
| 40107 | refresh_token无效，请传入最新的refresh_token | 传入refresh_token已失效，失效原因一般是由于refresh_token已被使用，或授权账号重新授权并生成了新的Token |
| 40113 | App不存在或者状态异常 | Access_Token 对应的开发者应用状态异常，请登陆开发者后台查看确认应用是否存在以及状态是否启用 |
| 40115 | 授权码无效 | auth_code 已失效，失效原因：1. 已使用 2. 已过10分钟有效期 3. 同账号重新授权生成 |
| 40115 | auth_code已经被使用，请重新授权 | auth_code 已失效，失效原因：1. 已使用 2. 已过10分钟有效期 3. 同账号重新授权生成 |
| 40118 | The API is not in allow list. Please apply for the allow list and retry. | 接口未上线，Access_Token对应开发者无对应接口请求权限 |
| 40119 | The developer and the advertiser do not belong to the same group. Please contact the administrator. | 线索、评论这类相对敏感的物料接口存在主体校验，如果开发者主体和操作的广告主主体不一致，接口会报错提示，如需操作需在申请授权时同时申请敏感物料授权，即授权链接中拼接参数material_auth=1 |

## 系统相关

| code | msg | 排查建议 |
|------|-----|----------|
| 50000 | SYS_ERROR | 系统请求超时，可以重试 |

## 限制相关

| code | msg | 排查建议 |
|------|-----|----------|
| 61002 | DEV_HEALTH | 当前开发者账号日累计调用接口次数超限 |
| 61003 | CUS_HEALTH | 调用广告主账户和其他同主体广告主账户使用受限 |

## 官方文档链接
[返回码](https://open.oceanengine.com/labels/12/docs/1699633774328846?origin=left_nav)