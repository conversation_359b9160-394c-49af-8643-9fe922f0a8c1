# 获取千川账户类型

## 接口描述
获取千川账户类型

## 请求地址
https://api.oceanengine.com/open_api/v1.0/qianchuan/advertiser/type/get/

## 请求方法
GET

## 请求Header
| 字段 | 类型 | 描述 |
|------|------|------|
| Access-Token | string | 必填，授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数
| 字段 | 类型 | 描述 |
|------|------|------|
| advertiser_ids | number[] | 必填，千川广告主账户id，一次请求不超过20个 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v1.0/qianchuan/advertiser/type/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == "__main__":
    advertiser_ids_list = ADVERTISER_IDS
    advertiser_ids = json.dumps(advertiser_ids_list)
    # Args in JSON format
    my_args = "{\"advertiser_ids\": %s}" % (advertiser_ids)
    print(get(my_args))
```

## 应答字段
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| └─ list | object[] | 广告主数据列表 |
| └─ ecp_type | string | 账户类型，可选值：<br>• SHOP：商家<br>• SHOP_STAR：商家达人<br>• COMMON_STAR：普通达人<br>• AGENT：机构 |
| └─ advertiser_id | number | 广告主id |
| └─ shop_business_type | string | 商家类型，可选值：<br>• UNKOWN：普通商家<br>• SHOP_RETAIL：即时零售商家 |
| └─ request_id | string | 请求日志id |

## 应答示例
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "list": [
      {
        "advertiser_id": 123456789,
        "ecp_type": "SHOP",
        "shop_business_type": "UNKOWN"
      },
      {
        "advertiser_id": 987654321,
        "ecp_type": "SHOP_STAR",
        "shop_business_type": "SHOP_RETAIL"
      },
      {
        "advertiser_id": 111222333,
        "ecp_type": "COMMON_STAR",
        "shop_business_type": "UNKOWN"
      }
    ]
  },
  "request_id": "20240101123456789"
}
```

## 官方文档链接
https://open.oceanengine.com/labels/12/docs/1754620816918532?origin=left_nav