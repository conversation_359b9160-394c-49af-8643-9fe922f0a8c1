# 巨量广告 - 获取图片素材

## 接口描述
通过此接口，用户可以获取经过一定条件过滤后的广告主下创意素材库的图片及图片信息。

## 请求地址
```
GET https://api.oceanengine.com/open_api/2/file/image/get/
```

## 请求方法
**GET**

## 请求Header
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数
| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主ID |
| filtering | object | 可选 | 图片过滤条件 |
| └─ image_ids | string[] | 可选 | 图片ids，可以根据图片ids（创意中使用的图片key，存在一张图片对应多个image_ids的情况）进行过滤<br/>数量限制：<=100<br/>注意：image_ids、material_ids、signatures只能选择一个进行过滤 |
| └─ material_ids | number[] | 可选 | 素材id列表，可以根据material_ids（素材报表使用的id，一个素材唯一对应一个素材id）进行过滤<br/>数量限制：<=100<br/>注意：image_ids、material_ids、signatures只能选择一个进行过滤 |
| └─ signatures | string[] | 可选 | md5值列表，可以根据素材的md5进行过滤<br/>数量限制：<=100<br/>注意：image_ids、material_ids、signatures只能选择一个进行过滤 |
| └─ width | double | 可选 | 图片宽度 |
| └─ height | double | 可选 | 图片高度 |
| └─ ratio | double[] | 可选 | 图片宽高比，eg: [1.7, 2.5]，输入1.7则搜索满足宽高比介于1.65-1.75之间的图片，即精度上下浮动0.05 |
| └─ start_time | string | 可选 | 根据图片上传时间进行过滤的起始时间，与end_time搭配使用，格式：yyyy-mm-dd |
| └─ end_time | string | 可选 | 根据图片上传时间进行过滤的截止时间，与start_time搭配使用，格式：yyyy-mm-dd |
| page | number | 可选 | 页码，默认值1 |
| page_size | number | 可选 | 页面大小，默认值20 |

## 请求示例
```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse  # noqa

ACCESS_TOKEN = "xxx"
PATH = "/open_api/2/file/image/get/"

def build_url(path, query=""):
    # type: (str, str) -> str
    """
    Build request URL
    :param path: Request path
    :param query: Querystring
    :return: Request URL
    """
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    # type: (str) -> dict
    """
    Send GET request
    :param json_str: Args in JSON format
    :return: Response in JSON format
    """
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    advertiser_id = ADVERTISER_ID
    image_ids_list = IMAGE_IDS
    image_ids = json.dumps(image_ids_list)
    material_ids_list = MATERIAL_IDS
    material_ids = json.dumps(material_ids_list)
    signatures_list = SIGNATURES
    signatures = json.dumps(signatures_list)
    width = WIDTH
    height = HEIGHT
    ratio_list = RATIO
    ratio = json.dumps(ratio_list)
    start_time = START_TIME
    end_time = END_TIME
    page = PAGE
    page_size = PAGE_SIZE

    # Args in JSON format
    my_args = "{\"advertiser_id\": \"%s\", \"filtering\": {\"image_ids\": %s, \"material_ids\": %s, \"signatures\": %s, \"width\": \"%s\", \"height\": \"%s\", \"ratio\": %s, \"start_time\": \"%s\", \"end_time\": \"%s\"}, \"page\": \"%s\", \"page_size\": \"%s\"}" % (advertiser_id, image_ids, material_ids, signatures, width, height, ratio, start_time, end_time, page, page_size)
    print(get(my_args))
```

## 应答参数
| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码,详见【附录-返回码】 |
| message | string | 返回信息,详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ list | object[] | 图片素材列表 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ id | string | 图片ID |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ material_id | number | 素材id，即多合一报表中的素材id，一个素材唯一对应一个素材id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ size | number | 图片大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ width | number | 图片宽度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ height | number | 图片高度 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ url | string | 图片预览地址，仅限同主体进行素材预览查看，若非同主体会返回"素材所属主体与开发者主体不一致无法获取URL"，链接仅做预览使用，预览链接有效期为1小时 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ format | string | 图片格式 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ signature | string | 图片md5 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ create_time | string | 素材的上传时间，格式："yyyy-mm-dd HH:MM:SS" |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ filename | string | 素材的文件名 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ aigc | bool | 素材是否是aigc生成 |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_number | number | 总数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 当前页 |
| request_id | string | 请求日志id |

## 应答示例
```json
{
  "message": "OK",
  "code": 0,
  "data": {}
}
```

## 官方文档链接
[获取图片素材](https://open.oceanengine.com/labels/7/docs/1696710601254912?origin=left_nav)