# 巨量本地推 - 查看积分处置详情

## 接口描述
查看积分处置详情

## 请求地址
`GET https://api.oceanengine.com/open_api/v3.0/security/score_disposal_info/get/`

## 请求方法
GET

## 请求Header

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| Access-Token | string | 必填 | 授权access_token，可以通过【获取Access token】接口获取 |

## 请求参数

| 字段 | 类型 | 是否必填 | 描述 |
|------|------|----------|------|
| advertiser_id | number | 必填 | 广告主id |
| business_line | string | 必填 | 业务线，允许值：<br/>• `AD` - 广告<br/>• `LOCALAD` - 本地推 |
| page | number | 可选 | 页数，默认值：`1`，page范围为[1,99999] |
| page_size | number | 可选 | 页面大小，默认值：`10`，page_size范围为[1,100] |
| order_field | string | 可选 | 排序字段，允许值：<br/>• `disposal_start_time` - 处置开始时间<br/>• `disposal_end_time` - 处置结束时间 |
| order_type | string | 可选 | 排序方式，可选值：<br/>• `ASC` - 升序，默认<br/>• `DESC` - 降序 |
| filtering | object | 必填 | 过滤器 |
| ├─ disposal_action | string | 可选 | 处置行为，允许值：<br/>• `ACCOUNTCLEAR` - 账号封停+主体限制新开<br/>默认值：`ACCOUNTCLEAR` |
| ├─ disposal_status | string | 可选 | 处置状态，允许值：<br/>• `DISPOSAL` - 已处置<br/>默认值：`DISPOSAL` |

## 请求示例

```python
# coding=utf-8
import json
import requests
from six import string_types
from six.moves.urllib.parse import urlencode, urlunparse

ACCESS_TOKEN = "xxx"
PATH = "/open_api/v3.0/security/score_disposal_info/get/"

def build_url(path, query=""):
    scheme, netloc = "https", "api.oceanengine.com"
    return urlunparse((scheme, netloc, path, "", query, ""))

def get(json_str):
    args = json.loads(json_str)
    query_string = urlencode({k: v if isinstance(v, string_types) else json.dumps(v) for k, v in args.items()})
    url = build_url(PATH, query_string)
    headers = {
        "Access-Token": ACCESS_TOKEN,
    }
    rsp = requests.get(url, headers=headers)
    return rsp.json()

if __name__ == '__main__':
    advertiser_id = ADVERTISER_ID
    business_line = BUSINESS_LINE
    page = PAGE
    page_size = PAGE_SIZE
    order_field = ORDER_FIELD
    order_type = ORDER_TYPE
    disposal_action = DISPOSAL_ACTION
    disposal_status = DISPOSAL_STATUS
    my_args = "{\"advertiser_id\": \"%s\", \"business_line\": \"%s\", \"page\": \"%s\", \"page_size\": \"%s\", \"order_field\": \"%s\", \"order_type\": \"%s\", \"filtering\": {\"disposal_action\": \"%s\", \"disposal_status\": \"%s\"}}" % (advertiser_id, business_line, page, page_size, order_field, order_type, disposal_action, disposal_status)
    print(get(my_args))
```

## 响应参数

| 字段 | 类型 | 描述 |
|------|------|------|
| code | number | 返回码，详见【附录-返回码】 |
| message | string | 返回信息，详见【附录-返回码】 |
| data | json | json返回值 |
| ├─ disposal_info_list | object[] | 处置信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ advertiser_id | number | 广告主id |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ disposal_action | string | 处置行为，枚举值：<br/>• `ACCOUNTCLEAR` - 账户封停+主体限制新建账户 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ disposal_term | string | 处置时长 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ disposal_start_time | string | 处置开始时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ disposal_end_time | string | 处置结束时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ disposal_status | string | 处置状态，枚举值：<br/>• `DISPOSAL` - 已处置 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ disposal_score | number | 落罚分值 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ outer_text | string | 对外文案 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ disposal_info_id | number | 处罚单id |
| ├─ page_info | object | 分页信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page | number | 页数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ page_size | number | 页面大小 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total | number | 总数 |
| &nbsp;&nbsp;&nbsp;&nbsp;├─ total_page | number | 总页数 |
| request_id | string | 请求日志id |

## 响应示例

```json
{
  "message": "OK",
  "code": 0,
  "data": {
    "disposal_info_list": [
      {
        "advertiser_id": **********,
        "disposal_action": "ACCOUNTCLEAR",
        "disposal_term": "30天",
        "disposal_start_time": "2023-01-01 00:00:00",
        "disposal_end_time": "2023-01-31 23:59:59",
        "disposal_status": "DISPOSAL",
        "disposal_score": 100,
        "outer_text": "因违反广告政策，账户被封停",
        "disposal_info_id": *********
      }
    ],
    "page_info": {
      "page": 1,
      "page_size": 10,
      "total": 1,
      "total_page": 1
    }
  },
  "request_id": "20240712123456789abcdef"
}
```

## 官方文档链接
https://open.oceanengine.com/labels/37/docs/****************?origin=left_nav